from abc import ABC, abstractmethod
from typing import List
from uuid import UUID

from sqlalchemy import and_, bindparam, func, or_, select, text
from sqlalchemy.engine import Row
from sqlalchemy.orm import Session

from accounts.adapters import orm as acc_orm
from automation.adapters import orm as automation_rule
from automation.domain.model import RuleAction
from rate_plans.adapters import orm as rate_orm
from redis.domain import model
from sim.adapters import orm


class AbstractRepository(ABC):
    @abstractmethod
    def get_imsis(self, account_id: int, rate_plan_id: int | None = None) -> list[str]:
        ...

    @abstractmethod
    def get_account_name(self, account_id: int) -> str:
        ...

    @abstractmethod
    def get_rules(
        self, account_id: int, rule_uuid: UUID | None = None
    ) -> model.RuleInfo | model.Rules:
        ...

    @abstractmethod
    def get_rule_status(self, account_id: int, rule_uuid: UUID) -> bool:
        ...

    @abstractmethod
    def get_deleted_rule(self, account_id: int, rule_uuid: UUID) -> bool:
        ...

    @abstractmethod
    def get_get_rule_by_uuid(self, rule_uuid: UUID) -> int:
        ...


class DatabaseRepository(AbstractRepository):
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def get_imsis(self, account_id: int, rate_plan_id: int | None = None) -> list[str]:
        sim_card, allocation = orm.sim_card, orm.sim_allocation
        conditions = [allocation.c.account_id == account_id]

        if rate_plan_id is not None:
            conditions.append(allocation.c.rate_plan_id == rate_plan_id)

        query = (
            select(sim_card.c.imsi)
            .select_from(sim_card.join(allocation))
            .where(*conditions)
        )
        imsis = self.session.execute(query).all()
        return [imsi.imsi for imsi in imsis]

    def get_account_name(self, account_id: int) -> str:
        account = acc_orm.account
        query = select(account.c.name).where(account.c.id == account_id)
        account_info = self.session.execute(query).first()
        if not isinstance(account_info, Row):
            raise TypeError("Expected account_info to be of type Row")
        return account_info.name

    def get_rules(
        self, account_id: int, rule_uuid: UUID | None = None
    ) -> model.RuleInfo | model.Rules:
        rule = automation_rule.rules
        rate_plan = rate_orm.RatePlan
        TRUE = True
        if rule_uuid:
            query = select(
                rule.c.uuid, rule.c.status, rule.c.data_volume, rule.c.unit
            ).where(
                rule.c.account_id == account_id,
                rule.c.uuid == rule_uuid,
                rule.c.status == TRUE,
            )
            rules = self.session.execute(query).all()
        else:
            query = select(
                rule.c.uuid, rule.c.status, rule.c.data_volume, rule.c.unit
            ).where(rule.c.account_id == account_id, rule.c.status == TRUE)
            rules = self.session.execute(query).all()
        rule_list = [rule.uuid for rule in rules]

        account_name = self.get_account_name(account_id)

        rule_info = model.RuleInfo(
            IMSIs=self.get_imsis(account_id),
            accountId=account_id,
            accountName=account_name,
            rules=[],
        )

        if rule_list:
            actions_result = []
            for rules_uuid in rule_list:
                action = automation_rule.actions
                rule_action = automation_rule.rules_action
                rule_action_mapping = automation_rule.rules_action_mapping
                query = (
                    select(
                        rule_action.c.rules_uuid,
                        action.c.action.label("name"),
                        rule_action.c.action.label("action"),
                        rule_action.c.action_value.label("action_value"),
                        rule_action_mapping.c.source.label("source"),
                        rule_action_mapping.c.target.label("target"),
                    )
                    .join(action, action.c.id == rule_action.c.actions_id)
                    .outerjoin(
                        rule_action_mapping,
                        and_(
                            rule_action_mapping.c.rules_uuid
                            == rule_action.c.rules_uuid,
                            rule_action.c.action == RuleAction.CHANGE_RATE_PLAN,
                        ),
                    )
                    .filter(rule_action.c.rules_uuid == rules_uuid)
                )
                response = self.session.execute(query).all()
                actions_result.extend(response)

            notifications_query = select(
                [text("name"), text("notification"), text("email"), text("rules_uuid")]
            ).select_from(func.get_notifications_by_rules_uuid(rule_list))

            rate_plan_query = select(rate_plan.name).where(
                rate_plan.id == bindparam("rate_plan_id")
            )

            notifications_result = self.session.execute(notifications_query).fetchall()

            actions_map: dict[str, List[model.Action | model.RateplanAction]] = {}
            for row in actions_result:
                rules_uuid = row.rules_uuid
                if rules_uuid not in actions_map:
                    actions_map[rules_uuid] = []
                if row.source and row.target:
                    source_plan = self.session.execute(
                        rate_plan_query, {"rate_plan_id": row.source}
                    ).scalar()
                    target_plan = self.session.execute(
                        rate_plan_query, {"rate_plan_id": row.target}
                    ).scalar()
                    actions_map[rules_uuid].append(
                        model.RateplanAction(
                            name=row.name,
                            action=row.action,
                            source=row.source,
                            target=row.target,
                            actionValue=row.action_value,
                            sourcePlan=source_plan,
                            targetPlan=target_plan,
                        )
                    )
                elif row.source is None and row.target is not None:
                    target_plan = self.session.execute(
                        rate_plan_query, {"rate_plan_id": row.target}
                    ).scalar()
                    source_plan = None
                    actions_map[rules_uuid].append(
                        model.RateplanAction(
                            name=row.name,
                            action=row.action,
                            source=row.source,
                            target=row.target,
                            actionValue=row.action_value,
                            sourcePlan=source_plan,
                            targetPlan=target_plan,
                        )
                    )
                else:
                    actions_map[rules_uuid].append(
                        model.Action(
                            name=row.name,
                            action=row.action,
                            actionValue=row.action_value,
                        )
                    )

            notifications_map: dict[str, List[model.Notification]] = {}
            for row in notifications_result:
                rules_uuid = row.rules_uuid
                if rules_uuid not in notifications_map:
                    notifications_map[rules_uuid] = []
                notifications_map[rules_uuid].append(
                    model.Notification(
                        name=row.name, notification=row.notification, email=row.email
                    )
                )
        for row in rules:
            if not row.uuid:
                continue
            rule_info.rules.append(
                model.Rules(
                    rulesUuidParam=row.uuid,
                    simUsageLimit=row.data_volume or 0,
                    status=row.status,
                    unit=row.unit,
                    actions=actions_map.get(row.uuid) or [],
                    notifications=notifications_map.get(row.uuid) or [],
                )
            )
        if rule_uuid:
            return rule_info.rules[0]
        return rule_info

    def get_rule_status(self, account_id: int, rule_uuid: UUID) -> bool:
        rule = automation_rule.rules
        FALSE = False
        query = select(rule.c.status).where(
            rule.c.account_id == account_id,
            rule.c.uuid == rule_uuid,
            rule.c.status == FALSE,
        )
        result = self.session.execute(query).scalar()
        return bool(result)

    def get_deleted_rule(self, account_id: int, rule_uuid: UUID) -> bool:
        rule = automation_rule.rules
        TRUE = True
        FALSE = False
        query = select(rule.c.uuid).where(
            rule.c.account_id == account_id,
            rule.c.uuid == rule_uuid,
            or_(rule.c.status == TRUE, rule.c.status == FALSE),
        )
        result = self.session.execute(query).scalar()
        return bool(result)

    def get_get_rule_by_uuid(self, rule_uuid: UUID) -> int:
        rule = automation_rule.rules
        query = select(
            rule.c.account_id,
        ).where(rule.c.uuid == rule_uuid)
        result = self.session.execute(query).first()
        if not isinstance(result, Row):
            raise TypeError(
                f"Expected result to be of type Row, but got {type(result)}"
            )
        return result.account_id

class BillingError(Exception):
    ...


class BillingObjectDoesNotExist(BillingError):
    ...


class BillingCycleDoesNotExist(BillingObjectDoesNotExist):
    ...


class CalculationIncomplete(BillingError):
    ...


class CalculationInProgress(CalculationIncomplete):
    def __init__(self):
        super().__init__("Calculation in progress.")


class CalculationFailed(CalculationIncomplete):
    def __init__(self):
        super().__init__("Calculation failed.")


class InvoiceDoesNotExist(BillingObjectDoesNotExist):
    def __init__(self, invoice_id):
        super().__init__(f"Invoice with id:{invoice_id} does not exist")


class AdjustmentDoesNotExist(BillingObjectDoesNotExist):
    def __init__(self, invoice_id, adjustment_id):
        super().__init__(
            (
                f"Adjustment with id:{adjustment_id} does not exist "
                f"for invoice with id:{invoice_id}."
            )
        )


class InvoiceAlreadyPublished(BillingError):
    def __init__(self):
        super().__init__("The invoice has already been published.")


class InvoiceHasNotBeenPublished(BillingError):
    def __init__(self):
        super().__init__("The invoice has not been published yet.")

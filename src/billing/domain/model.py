from dataclasses import dataclass, field
from datetime import date, datetime, timedelta
from decimal import Decimal
from enum import Enum
from itertools import chain
from operator import attrgetter
from typing import Iterable, Optional

from more_itertools import ilen

from accounts.domain.model import AccountStatus, PaymentTerms, SalesChannel
from billing.exceptions import (
    AdjustmentDoesNotExist,
    CalculationFailed,
    CalculationInProgress,
    InvoiceAlreadyPublished,
    InvoiceHasNotBeenPublished,
)
from common.types import IMSI, Month, Service, ServiceUsage
from sim.domain.model import ICCID, MSISDN, SimStatus


@dataclass
class Account:
    id: int
    status: AccountStatus
    is_billable: bool
    sales_channel: SalesChannel
    payment_terms: PaymentTerms
    sim_charge: Decimal
    contract_end_date: date


@dataclass
class BillingAccount:
    id: int
    name: str
    logo_key: str
    status: AccountStatus
    is_billable: bool
    industry_vertical: str
    currency: str
    contract_end_date: date
    sim_charge: Decimal = Decimal(0)


class BillingCycle:
    def __init__(self, month: Month):
        self.month = month
        self._invoices: dict[int, Invoice] = {}
        self._sim_usage: list[SimUsage] = []

    @property
    def account_ids(self) -> list[int]:
        return list(self._invoices)

    @property
    def invoices(self) -> list["Invoice"]:
        return list(self._invoices.values())

    def make_invoice(self, account_id: int, payment_terms: PaymentTerms) -> "Invoice":
        """Create Invoice for calculation."""
        invoice = Invoice(account_id, payment_terms)
        self._invoices[account_id] = invoice
        return self._invoices[account_id]

    def get_invoice(self, account_id: int) -> Optional["Invoice"]:
        """Get calculated invoice, if it exists."""
        return self._invoices.get(account_id, None)

    @property
    def sim_usage(self) -> Iterable["SimUsage"]:
        return iter(self._sim_usage)

    def insert_sim_usage(self, records: Iterable["SimUsage"]) -> None:
        self._sim_usage.extend(records)

    def remove_sim_usage_by_imsis(self, imsi_list: Iterable[IMSI]) -> None:
        imsis_to_remove = set(imsi_list)
        filtered_sim_usage = list(
            filter(lambda u: u.imsi not in imsis_to_remove, self._sim_usage)
        )
        self._sim_usage = filtered_sim_usage


class AdjustmentType(str, Enum):
    ACCOUNT_SETUP = "Account Setup"
    ACTIVATION_ADJUSTMENT = "Activation Adjustment"
    DATA_CHARGE_ADJUSTMENT = "Data Charge Adjustment"
    MONTHLY_ACCOUNT_MINIMUM = "Monthly Account Minimum"
    OTHER_CHARGE_ONE_TIME = "Other Charge - One Time"
    PREMIER_SUPPORT = "Premier Support"
    PRIVATE_APN_MONTHLY = "Private APN (Monthly)"
    PRIVATE_APN_SETUP_ONE_OFF = "Private APN Setup (One-off)"
    SIM_FEE = "SIM Fee"
    SMS_CHARGE_ADJUSTMENT = "SMS Charge Adjustment"
    SUBSCRIPTION_CHARGE_ADJUSTMENT = "Subscription Charge Adjustment"
    SUSPENDED_FEE = "Suspended Fee"
    TRAINING = "Training"


@dataclass
class Adjustment:
    id: int | None
    date: date
    type: AdjustmentType | None
    amount: Decimal


# New billing logic
@dataclass
class SubscriptionSimAllocation:
    id: int | None
    invoice_id: int
    sim_id: int
    allocation_id: int
    allocation_date: date


# New billing logic


class InvoiceRatingState(str, Enum):
    IN_PROGRESS = "IN_PROGRESS"
    GENERATED = "GENERATED"
    FAILED = "FAILED"


class Invoice:
    id: int
    billing_cycle: BillingCycle
    _new_sims: int | None
    _total_account_charge: Decimal | None = Decimal(0)
    published_at: datetime | None = None
    account: Account | None = None
    _adjustments: list[Adjustment]

    def __init__(self, account_id: int, due_days: int):
        self.account_id = account_id
        self.due_days = due_days
        self.subscriptions: list[Subscription] = []
        self.usages: list[ServiceUsage] = []
        self._new_sims = None
        self._total_account_charge = Decimal(0)
        self.rating_state: InvoiceRatingState = InvoiceRatingState.IN_PROGRESS

    @property
    def due_date(self) -> date | None:
        if self.invoice_date:
            return self.invoice_date + timedelta(days=self.due_days)
        return None

    @property
    def invoice_date(self) -> date | None:
        return self.published_at.date() if self.published_at else None

    @property
    def is_published(self) -> bool:
        return self.published_at is not None

    @property
    def adjustments(self) -> tuple[Adjustment, ...]:
        return tuple(self._adjustments)

    @property
    def has_rating(self) -> bool:
        return any(subscription.sims_active for subscription in self.subscriptions)

    @property
    def is_billable(self) -> bool:
        return self.account is not None and self.account.is_billable

    def check_rating_state(self):
        if self.rating_state == InvoiceRatingState.IN_PROGRESS:
            raise CalculationInProgress()
        elif self.rating_state == InvoiceRatingState.FAILED:
            raise CalculationFailed()

    def publish(self) -> None:
        self.check_rating_state()
        if self.is_published:
            raise InvoiceAlreadyPublished()
        self.published_at = datetime.utcnow()

    def unpublish(self) -> None:
        self.check_rating_state()
        if not self.is_published:
            raise InvoiceHasNotBeenPublished()
        self.published_at = None

    def add_adjustment(self, adjustment: Adjustment) -> Adjustment:
        """Add Adjustment for Invoice."""
        self.check_rating_state()
        if self.is_published:
            raise InvoiceAlreadyPublished()
        self._adjustments.append(adjustment)
        return adjustment

    def get_adjustment(self, adjustment_id: int) -> Adjustment:
        """Get adjustment by ID."""
        for adjustment in self.adjustments:
            if adjustment.id == adjustment_id:
                return adjustment
        else:
            raise AdjustmentDoesNotExist(self.id, adjustment_id)

    def remove_adjustment(self, adjustment: Adjustment) -> None:
        """Remove Adjustment for Invoice."""
        self.check_rating_state()
        if self.is_published:
            raise InvoiceAlreadyPublished()
        self._adjustments.remove(adjustment)

    def reset(self, payment_terms: PaymentTerms) -> None:
        """Clear subscription data and update payment terms."""
        if self.is_published:
            raise InvoiceAlreadyPublished()
        self.due_days = payment_terms
        self.subscriptions.clear()

    def add_subscription(
        self,
        rate_plan_id: int,
        rate_plan_name: str,
        access_fee: Decimal,
        sims_total: int,
        sim_charge: Decimal,
        sims_active: int | None = None,
    ) -> None:
        subscription = Subscription(
            rate_plan_id=rate_plan_id,
            name=rate_plan_name,
            access_fee=access_fee,
            sims_total=sims_total,
            _sims_active=sims_active,
            sim_charge=sim_charge,
        )
        self.subscriptions.append(subscription)

    def add_sim(
        self,
        rate_plan_id: int,
        sim_id: int,
        imsi: IMSI,
        iccid: ICCID,
        msisdn: MSISDN,
        first_time_activated: bool,
        sim_status: SimStatus,  # New billing logic - added into subscription_sim table
    ) -> None:
        subscription = next(
            (s for s in self.subscriptions if s.rate_plan_id == rate_plan_id), None
        )
        if not subscription:
            raise AssertionError(f"Subscription for {rate_plan_id=} must be added.")
        sim = SubscriptionSIM(
            sim_id=sim_id,
            imsi=imsi,
            iccid=iccid,
            msisdn=msisdn,
            first_time_activated=first_time_activated,
            sim_status=sim_status,
        )
        subscription.sims.append(sim)

    @property
    def new_sims(self) -> int:
        """The number of SIM cards activated for the first time this month."""
        if self._new_sims is None:
            all_sims = chain.from_iterable(s.sims for s in self.subscriptions)
            self._new_sims = ilen(filter(attrgetter("first_time_activated"), all_sims))
        return self._new_sims

    @property
    def total_account_charge(self) -> Decimal:
        if self._total_account_charge is None:
            self._total_account_charge = Decimal(0)
        return self._total_account_charge


@dataclass
class Subscription:
    """Billing data for a specific rate plan and billing cycle."""

    _invoice: Invoice = field(init=False)
    rate_plan_id: int
    name: str
    access_fee: Decimal
    sims_total: int
    sim_charge: Decimal

    sims: list["SubscriptionSIM"] = field(default_factory=list)
    _sims_active: int | None = None

    # New billing logic - Get count of active sim_status
    @property
    def sims_active(self) -> int:
        if self._sims_active is not None:
            return self._sims_active
        self._sims_active = sum(
            1
            for sim in self.sims
            if sim.sim_status is None
            or sim.sim_status != SimStatus.READY_FOR_ACTIVATION
        )
        return self._sims_active

    # New billing logic - Get count of active sim_status

    @property
    def charge(self) -> Decimal:
        return self.sims_active * self.access_fee


@dataclass
class SubscriptionSIM:
    subscription: Subscription = field(init=False)
    sim_id: int
    imsi: IMSI
    iccid: ICCID
    msisdn: MSISDN
    first_time_activated: bool
    sim_status: SimStatus | None = None
    _usage_summary: list[ServiceUsage] | None = None

    usage: list["SimUsage"] = field(default_factory=list)

    @property
    def sim_fee_charge(self) -> Decimal:
        return self.subscription.access_fee

    @property
    def rate_plan_id(self) -> int:
        return self.subscription.rate_plan_id

    @property
    def usage_summary(self) -> list[ServiceUsage]:
        if self._usage_summary is None:
            summary = {
                service: ServiceUsage(
                    service, 0, Decimal(0), Decimal(0), Decimal(0), Decimal(0)
                )
                for service in list(Service)
            }
            for record in self.usage:
                summary[record.service].volume += record.volume
                if record.charge is not None:
                    summary[record.service].charge += record.charge
            self._usage_summary = list(summary.values())
        return self._usage_summary


@dataclass
class SimUsage:
    _billing_cycle: BillingCycle = field(init=False)
    imsi: IMSI
    service: Service
    volume: int
    charge: Decimal | None = None


@dataclass
class ReconciliationDifference:
    simUsage: int | None
    cdrUsage: int | None
    variance: int | None


@dataclass
class ReconciliationDetails:
    simImsi: str | None = None
    cdrImsi: str | None = None
    dataDetails: ReconciliationDifference | None = None
    voiceDetails: ReconciliationDifference | None = None
    smsDetails: ReconciliationDifference | None = None


@dataclass
class ReconciliationAgg:
    service: str
    simImsi: str | None = None
    cdrImsi: str | None = None
    simUsage: int | None = None
    cdrUsage: int | None = None
    variance: int | None = None


@dataclass
class InvoiceOverageCharge:
    id: int | None
    invoice_id: int
    sim_count: int
    service: Service
    total_allowance: int
    total_usage: int
    usage_variance: int
    usage_variance_coverted: Decimal
    total_charge: Decimal


@dataclass
class InvoiceFixedChargeDetails:
    account_charge: Decimal


@dataclass
class InvoiceFixedCharge(InvoiceFixedChargeDetails):
    id: int | None
    invoice_id: int

import re
from abc import ABC, abstractmethod
from typing import Iterable, Iterator, no_type_check
from uuid import UUID

from sqlalchemy import (
    and_,
    asc,
    delete,
    desc,
    func,
    literal_column,
    nulls_first,
    nulls_last,
    select,
)
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from sqlalchemy.sql.functions import count

from app.config import logger
from automation.adapters import orm
from automation.adapters.exceptions import RuleNotFound
from automation.domain import model
from automation.exceptions import (
    CreateRuleError,
    RuleAlreadyExist,
    RuleCategoryError,
    RuleRelyError,
)
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching, apply_search_to_sql


class AbstractAutomationRepository(ABC):
    @abstractmethod
    def create_rule(
        self,
        rules_detail: list[model.Rules],
    ) -> bool:
        ...

    @abstractmethod
    def get_rule_by_uuid(self, rule_uuid: UUID) -> model.RuleData:
        ...

    @abstractmethod
    def get_actions_by_rule_uuid(
        self, rule_uuid: UUID
    ) -> list[model.Action | model.RateplanAction]:
        ...

    @abstractmethod
    def get_notification_by_rule_uuid(
        self, rule_uuid: UUID
    ) -> list[model.Notification]:
        ...

    @abstractmethod
    def get_rule(
        self,
        account_id: int | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterable[model.RulesData]:
        ...

    @abstractmethod
    def rule_type(
        self,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.RuleType]:
        ...

    @abstractmethod
    def rule_type_count(
        self,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def rule_category_count(
        self,
        type_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def rule_category(
        self,
        type_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.RuleCategoryDetails]:
        ...

    @abstractmethod
    def rule_category_by_id(
        self,
        category_id: int | None = None,
    ) -> model.RuleCategoryDetails:
        ...

    @abstractmethod
    def rule_definition_count(
        self,
        rule_category_id: int,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def rule_definition(
        self,
        rule_category_id: int,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.RuleDefinitionDetails]:
        ...

    @abstractmethod
    def get_actions(
        self,
        action_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Actions]:
        ...

    @abstractmethod
    def get_actions_count(
        self,
        action_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def get_notification_list(
        self,
        notification_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Notifications]:
        ...

    @abstractmethod
    def get_notification_count(
        self,
        notification_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def create_rules_action(
        self, rules_action_detail: list[model.RulesAction] | None, rules_uuid: UUID
    ) -> model.RuleDetails:
        ...

    @abstractmethod
    def create_rules_action_mapping(
        self, rules_action_mapping: model.RulesActionMapping, rules_uuid: UUID
    ) -> model.RuleDetails:
        ...

    @abstractmethod
    def create_rules_notification(
        self, rules_notification_detail: model.RulesNotification
    ) -> bool:
        ...

    @abstractmethod
    def reset_rules_action_and_notification(self, rules_uuid: UUID):
        ...

    @abstractmethod
    def update_rule(
        self,
        rules_detail: model.Rules,
    ) -> bool:
        ...

    @abstractmethod
    def update_rule_status_by_rule_uuid(
        self, rule_uuid: UUID, rule_status: bool
    ) -> None:
        ...

    @abstractmethod
    def update_lock_status_by_rule_uuid(
        self, rule_uuid: UUID, lock_status: bool
    ) -> None:
        ...

    @abstractmethod
    def get_rule_count(
        self,
        account_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def delete_rule(
        self,
        rules_uuid: UUID,
    ) -> None:
        ...

    @abstractmethod
    def delete_rule_by_account(
        self,
        id: int,
    ) -> None:
        ...

    @abstractmethod
    def count_rules_by_account(self, id: int) -> int:
        ...

    @abstractmethod
    def get_rule_rely(self, rule_category_id: int) -> model.RuleResponseRely:
        ...

    @abstractmethod
    def create_rule_rely(self, rule_rely: model.RuleDetailsRely) -> bool:
        ...

    @abstractmethod
    def get_rule_rely_by_id(self, rule_rely_id: int):
        ...

    @abstractmethod
    def update_rule_rely(
        self, rule_rely: model.RuleDetailsRely, rule_rely_id: int
    ) -> None:
        ...


class DatabaseAutomationRepository(AbstractAutomationRepository):
    def __init__(self, session: Session) -> None:
        self.session = session

    def create_rules_action(
        self, rules_action_detail: list[model.RulesAction] | None, rules_uuid: UUID
    ):
        if rules_action_detail:
            for rule in rules_action_detail:
                rule.actions_id = self.session.scalar(
                    select(orm.actions.c.id).where(orm.actions.c.action == rule.action)
                )
            # Add Rules_Action
            self.session.bulk_save_objects(rules_action_detail)
            self.session.commit()

        return model.RuleDetails(uuid=rules_uuid)

    def create_rules_action_mapping(
        self, rules_action_mapping: model.RulesActionMapping, rules_uuid: UUID
    ) -> model.RuleDetails:
        self.session.add(rules_action_mapping)
        self.session.commit()
        return model.RuleDetails(uuid=rules_uuid)

    def _notification_value_details(
        self,
        notification_value: list[model.NotificationValue],
        rules_notification_id: int,
    ):
        for value in notification_value:
            value.rules_notification_id = rules_notification_id

        return notification_value

    def create_rules_notification(
        self, rules_notification_detail: model.RulesNotification
    ) -> bool:
        if rules_notification_detail and rules_notification_detail.notification:
            # Add Rules_Notification
            self.session.add(rules_notification_detail)
            self.session.flush()

        if (
            rules_notification_detail.notification_value
            and rules_notification_detail.id
        ):
            # Add Notification_Values
            notification_value = self._notification_value_details(
                notification_value=rules_notification_detail.notification_value,
                rules_notification_id=rules_notification_detail.id,
            )
            self.session.bulk_save_objects(notification_value)
        self.session.commit()

        return True

    def create_rule(
        self,
        rules_detail: list[model.Rules],
    ) -> bool:
        # Create Rule
        try:
            self.session.add_all(rules_detail)
            self.session.flush()
            return True
        except IntegrityError as e:
            pattern = re.search("already exists.", str(e))
            if pattern:
                raise RuleAlreadyExist
            else:
                raise CreateRuleError

    def get_actions_by_rule_uuid(
        self, rule_uuid: UUID
    ) -> list[model.Action | model.RateplanAction]:
        action = orm.actions
        rule_action = orm.rules_action
        rule_action_mapping = orm.rules_action_mapping
        query = (
            select(
                action.c.action.label("name"),
                rule_action.c.action.label("action"),
                rule_action.c.action_value.label("action_value"),
                rule_action_mapping.c.source.label("source"),
                rule_action_mapping.c.target.label("target"),
            )
            .join(action, action.c.id == rule_action.c.actions_id)
            .outerjoin(
                rule_action_mapping,
                and_(
                    rule_action_mapping.c.rules_uuid == rule_action.c.rules_uuid,
                ),
            )
            .filter(rule_action.c.rules_uuid == rule_uuid)
        )
        response = self.session.execute(query).all()
        action_details: list[model.Action | model.RateplanAction] = []
        for row in response:
            if row.action == model.RuleAction.CHANGE_RATE_PLAN:
                action_details.append(
                    model.RateplanAction(
                        name=row.name,
                        action=row.action,
                        source=row.source,
                        target=row.target,
                        action_value=row.action_value,
                    ),
                )
            else:
                action_details.append(
                    model.Action(
                        name=row.name, action=row.action, action_value=row.action_value
                    )
                )
        return action_details

    def get_notification_by_rule_uuid(
        self, rule_uuid: UUID
    ) -> list[model.Notification]:
        notifications = orm.notifications
        rule_notification = orm.rules_notification
        notification_value = orm.notification_value
        query = (
            select(
                notifications.c.notification.label("name"),
                rule_notification.c.notification.label("notification"),
                func.array_agg(notification_value.c.notification_value).label(
                    "notification_value"
                ),
            )
            .join(
                notifications,
                notifications.c.id == rule_notification.c.notifications_id,
            )
            .join(
                notification_value,
                notification_value.c.rules_notification_id == rule_notification.c.id,
            )
            .filter(rule_notification.c.rules_uuid == rule_uuid)
            .group_by(notifications.c.id, rule_notification.c.notification)
        )
        return [
            model.Notification(**row) for row in self.session.execute(query).mappings()
        ]

    def get_rule_by_uuid(self, rule_uuid: UUID) -> model.RuleData:
        rule = orm.rules
        rule_type = orm.rule_type
        rule_category = orm.rule_category
        rule_definition = orm.rule_definition
        query = (
            select(
                rule.c.id,
                rule.c.uuid,
                rule.c.account_id,
                rule.c.rule_name,
                rule_type.c.id.label("rule_type_id"),
                rule_type.c.rule_type,
                rule_category.c.id.label("rule_category_id"),
                rule_category.c.category.label("rule_category"),
                rule_definition.c.id.label("rule_definition_id"),
                rule_definition.c.definition.label("rule_definition"),
                rule.c.data_volume,
                rule.c.unit,
                rule.c.status,
                rule.c.lock,
            )
            .join(rule_type, rule_type.c.id == rule.c.rule_type_id)
            .join(rule_category, rule_category.c.id == rule.c.rule_category_id)
            .join(rule_definition, rule_definition.c.id == rule.c.rule_definition_id)
            .filter(rule.c.uuid == rule_uuid)
        )
        result = self.session.execute(query).first()
        if not result:
            raise RuleNotFound(rule_uuid)

        rule_type_details = model.RulesTypeDetails(
            id=result.rule_type_id, rule_type=result.rule_type
        )
        rule_category_details = model.RulesCategoryDetails(
            id=result.rule_category_id, rule_category=result.rule_category
        )
        rule_definition_details = model.RulesDefinitionDetails(
            id=result.rule_definition_id, rule_definition=result.rule_definition
        )

        response = model.RuleData(
            id=result.id,
            uuid=result.uuid,
            account_id=result.account_id,
            rule_name=result.rule_name,
            data_volume=result.data_volume,
            unit=result.unit,
            status=result.status,
            lock=result.lock,
            rule_type=rule_type_details,
            rule_category=rule_category_details,
            rule_definition=rule_definition_details,
        )

        return response

    @no_type_check
    def get_rule(
        self,
        account_id: int | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterable[model.RulesData]:
        search_term = searching.search if searching else None
        get_all_rule_details = func.get_all_rule_details(
            search_term,
            account_id,
            ordering.field if ordering else None,
            ordering.order.lower() if ordering else None,
            pagination.offset if pagination else None,
            pagination.page_size if pagination else None,
        )

        get_rule_query = select("*").select_from(get_all_rule_details)

        for row in self.session.execute(get_rule_query).mappings():
            yield model.RulesData(
                id=row.id,
                uuid=row.uuid,
                account_id=row.account_id,
                rule_name=row.rule_name,
                data_volume=row.data_volume,
                unit=row.unit,
                status=row.status,
                lock=row.lock,
                rule_type=row.rule_type,
                rule_category=row.rule_category,
                rule_definition=row.rule_definition,
            )

    def rule_type(
        self,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.RuleType]:
        rule_type = orm.rule_type
        query = select(rule_type.c.id, rule_type.c.rule_type)
        if searching is not None:
            query = apply_search_to_sql(searching, model.RuleType, query)
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        if ordering is not None:
            order_direction, null_order = (
                (desc, nulls_last)
                if ordering.order.lower() == "desc"
                else (asc, nulls_first)
            )
            fields_mapper = {
                "id": rule_type.c.id,
                "rule_type": rule_type.c.rule_type,
            }
            if ordering.field in fields_mapper:
                order_field = fields_mapper[ordering.field]
            else:
                order_field = getattr(rule_type, ordering.field)
            query = query.order_by(null_order(order_direction(order_field))).order_by(
                order_direction(rule_type.c.id)
            )
        for row in self.session.execute(query).mappings():
            yield model.RuleType(**row)

    def rule_type_count(
        self,
        searching: Searching | None = None,
    ) -> int:
        rule_type = orm.rule_type
        query = select(count(rule_type.c.id))
        if searching is not None:
            query = apply_search_to_sql(searching, model.RuleType, query)
        return self.session.execute(query).scalar_one()

    def rule_category_count(
        self,
        type_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        rule_category = orm.rule_category
        query = select(count(rule_category.c.id))

        if type_id:
            query = query.filter(rule_category.c.rule_type_id == type_id)

        if searching is not None:
            query = apply_search_to_sql(searching, model.RuleCategory, query)
        return self.session.execute(query).scalar_one()

    def _rule_category_details(
        self,
        type_id: int | None = None,
        category_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        rule_category = orm.rule_category
        rule_type = orm.rule_type
        query = select(
            rule_category.c.rule_type_id,
            rule_category.c.id,
            rule_category.c.category,
            rule_category.c.rule_category_code,
            rule_type.c.rule_type,
        ).join(rule_type, rule_category.c.rule_type_id == rule_type.c.id)

        if type_id:
            query = query.filter(rule_category.c.rule_type_id == type_id)
        if category_id:
            query = query.filter(rule_category.c.id == category_id)

        if searching is not None:
            query = apply_search_to_sql(searching, model.RuleCategory, query)
        if ordering is not None:
            order_direction, null_order = (
                (desc, nulls_last)
                if ordering.order.lower() == "desc"
                else (asc, nulls_first)
            )
            fields_mapper = {
                "id": rule_category.c.id,
                "rule_type": rule_type.c.rule_type,
                "category": rule_category.c.category,
            }

            if ordering.field in fields_mapper:
                order_field = fields_mapper[ordering.field]
            else:
                order_field = getattr(model.RuleCategoryDetails, ordering.field)

            query = query.order_by(null_order(order_direction(order_field))).order_by(
                order_direction(rule_category.c.id)
            )
            query = query.order_by(rule_category.c.id.desc())
        else:
            query = query.order_by(rule_category.c.id.desc())
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        return query

    def rule_category(
        self,
        type_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.RuleCategoryDetails]:

        query = self._rule_category_details(
            searching=searching,
            type_id=type_id,
            pagination=pagination,
            ordering=ordering,
        )
        for row in self.session.execute(query).mappings():
            yield model.RuleCategoryDetails(**row)

    def rule_category_by_id(
        self,
        category_id: int | None = None,
    ) -> model.RuleCategoryDetails:

        query = self._rule_category_details(category_id=category_id)
        result = self.session.execute(query).first()

        if not result:
            logger.error(f"Rule Category Not Found with id:- {category_id}")
            raise RuleCategoryError(f"Rule Category Not Found with id:- {category_id}")
        response = model.RuleCategoryDetails(
            id=result.id,
            category=result.category,
            rule_type=result.rule_type,
            rule_type_id=result.rule_type_id,
            rule_category_code=result.rule_category_code,
        )

        return response

    def rule_definition_count(
        self,
        rule_category_id: int,
        searching: Searching | None = None,
    ) -> int:
        rule_definition = orm.rule_definition
        query = select(count(rule_definition.c.id)).filter(
            rule_definition.c.rule_category_id == rule_category_id
        )
        if searching is not None:
            query = apply_search_to_sql(searching, model.RuleDefinition, query)
        return self.session.execute(query).scalar_one()

    def rule_definition(
        self,
        rule_category_id: int,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.RuleDefinitionDetails]:
        rule_definition = orm.rule_definition
        rule_category = orm.rule_category
        query = select(
            rule_definition.c.id,
            rule_definition.c.definition,
            rule_category.c.category,
        ).join(rule_category, rule_definition.c.rule_category_id == rule_category.c.id)
        query = query.filter(rule_definition.c.rule_category_id == rule_category_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.RuleDefinition, query)
        if ordering is not None:
            order_direction, null_order = (
                (desc, nulls_last)
                if ordering.order.lower() == "desc"
                else (asc, nulls_first)
            )
            fields_mapper = {
                "id": rule_definition.c.id,
                "definition": rule_definition.c.definition,
            }

            if ordering.field in fields_mapper:
                order_field = fields_mapper[ordering.field]
            else:
                order_field = getattr(model.RuleDefinitionDetails, ordering.field)

            query = query.order_by(null_order(order_direction(order_field))).order_by(
                order_direction(rule_definition.c.id)
            )
            query = query.order_by(rule_definition.c.id.desc())
        else:
            query = query.order_by(rule_definition.c.id.desc())
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        for row in self.session.execute(query).mappings():
            yield model.RuleDefinitionDetails(**row)

    def get_actions(
        self,
        action_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Actions]:
        get_actions_orm = orm.actions
        query = select(get_actions_orm.c.id, get_actions_orm.c.action)
        if action_id is not None:
            query = query.filter(get_actions_orm.c.id == action_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.Actions, query)
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        if ordering is not None:
            order_direction, null_order = (
                (desc, nulls_last)
                if ordering.order.lower() == "desc"
                else (asc, nulls_first)
            )

            order_field = getattr(model.Actions, ordering.field)

            query = query.order_by(null_order(order_direction(order_field))).order_by(
                order_direction(get_actions_orm.c.id)
            )
        for row in self.session.execute(query).mappings():
            yield model.Actions(**row)

    def get_actions_count(
        self, action_id: int | None = None, searching: Searching | None = None
    ) -> int:
        action = orm.actions
        query = select(count(action.c.id))
        if action_id is not None:
            query = query.filter_by(id=action_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.Actions, query)
        return self.session.execute(query).scalar_one()

    def get_notification_list(
        self,
        notification_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Notifications]:
        source = orm.notifications
        query = select(source.c.id, source.c.notification)
        if notification_id is not None:
            query = query.filter(model.Notifications.id == notification_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.Notifications, query)
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        if ordering is not None:
            order_direction, null_order = (
                (desc, nulls_last)
                if ordering.order.lower() == "desc"
                else (asc, nulls_first)
            )

            order_field = getattr(model.Notifications, ordering.field)

            query = query.order_by(null_order(order_direction(order_field))).order_by(
                order_direction(source.c.id)
            )
        for row in self.session.execute(query).mappings():
            yield model.Notifications(**row)

    def get_notification_count(
        self,
        notification_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        notification = orm.notifications
        query = select(count(notification.c.id))
        if notification_id is not None:
            query = query.filter_by(id=notification_id)
        if searching is not None:
            query = apply_search_to_sql(searching, model.Notifications, query)
        return self.session.execute(query).scalar_one()

    def reset_rules_action_and_notification(self, rules_uuid: UUID) -> None:
        """
        This function deletes all the
        rules action notification and notification_value
        details from all related table with rules_uuid
        """
        action = orm.rules_action
        action_mapping = orm.rules_action_mapping
        notification = orm.rules_notification
        notification_value = orm.notification_value
        self.get_rule_by_uuid(rule_uuid=rules_uuid)

        subquery = select(notification.c.id).where(
            notification.c.rules_uuid == rules_uuid
        )

        with self.session.begin_nested():
            self.session.execute(
                delete(notification_value).where(
                    notification_value.c.rules_notification_id.in_(subquery)
                )
            )
            self.session.execute(
                delete(notification).where(notification.c.rules_uuid == rules_uuid)
            )
            self.session.execute(
                delete(action_mapping).where(action_mapping.c.rules_uuid == rules_uuid)
            )
            self.session.execute(
                delete(action).where(action.c.rules_uuid == rules_uuid)
            )
        return

    def update_rule(
        self,
        rules_detail: model.Rules,
    ) -> bool:
        try:
            rules_detail_orm = orm.rules

            # Update Rule
            update_rule = {
                rules_detail_orm.c.rule_type_id: rules_detail.rule_type_id,
                rules_detail_orm.c.rule_category_id: rules_detail.rule_category_id,
                rules_detail_orm.c.rule_definition_id: rules_detail.rule_definition_id,
                rules_detail_orm.c.rule_name: rules_detail.rule_name,
                rules_detail_orm.c.data_volume: rules_detail.data_volume,
                rules_detail_orm.c.status: rules_detail.status,
                rules_detail_orm.c.unit: rules_detail.unit,
                rules_detail_orm.c.lock: rules_detail.lock,
            }

            self.session.query(rules_detail_orm).filter(
                rules_detail_orm.c.uuid == rules_detail.uuid,
            ).update(update_rule)
            return True

        except IntegrityError as e:
            pattern = re.search("already exists.", str(e))
            if pattern:
                raise RuleAlreadyExist
            else:
                raise CreateRuleError

    def update_rule_status_by_rule_uuid(
        self, rule_uuid: UUID, rule_status: bool
    ) -> None:
        rules = orm.rules

        # Patch Rule
        update_rule = {
            rules.c.status: rule_status,
        }

        self.session.query(rules).filter(
            rules.c.uuid == rule_uuid,
        ).update(update_rule)

        self.session.commit()
        return

    def get_rule_count(
        self,
        account_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:

        search_term = searching.search if searching else None

        get_all_rule_details = func.get_all_rule_details(
            search_term,
            account_id,
        )

        query = select(count(literal_column("id"))).select_from(get_all_rule_details)

        return self.session.execute(query).scalar_one()

    def delete_rule(
        self,
        rules_uuid: UUID,
    ) -> None:
        rule = orm.rules

        query = delete(rule).where(rule.c.uuid == rules_uuid)

        self.session.execute(query)
        self.session.commit()

        return None

    def delete_rule_by_account(self, id: int) -> None:
        rules = orm.rules
        rules_action = orm.rules_action
        rules_notification = orm.rules_notification
        notification_value = orm.notification_value

        rules_account_query = select(rules.c.uuid).where(rules.c.account_id == id)

        # Delete from notification_value where rules_notification_id matches
        delete_notification_value_query = delete(notification_value).where(
            notification_value.c.rules_notification_id.in_(
                select(rules_notification.c.id).where(
                    rules_notification.c.rules_uuid.in_(rules_account_query)
                )
            )
        )
        self.session.execute(delete_notification_value_query)

        # Delete from rules_notification where the rules_uuid matches the rules table
        delete_notification_query = delete(rules_notification).where(
            rules_notification.c.rules_uuid.in_(rules_account_query)
        )
        self.session.execute(delete_notification_query)

        # Delete from rules_action where the rules_uuid matches the rules table
        delete_action_query = delete(rules_action).where(
            rules_action.c.rules_uuid.in_(rules_account_query)
        )
        self.session.execute(delete_action_query)

        # Delete from rules where account_id matches
        delete_rules_query = delete(rules).where(rules.c.account_id == id)
        self.session.execute(delete_rules_query)

        self.session.commit()
        return None

    def count_rules_by_account(self, id: int) -> int:
        rules = orm.rules

        # Count the rules where account_id matches
        count_query = select(func.count()).where(rules.c.account_id == id)

        # Execute the query and fetch the count
        result = self.session.execute(count_query).scalar_one()

        return result

    def update_lock_status_by_rule_uuid(
        self, rule_uuid: UUID, lock_status: bool
    ) -> None:
        rules = orm.rules

        # Patch Rule
        update_rule = {
            rules.c.lock: lock_status,
        }

        self.session.query(rules).filter(
            rules.c.uuid == rule_uuid,
        ).update(update_rule)
        self.session.query(rules).filter(
            rules.c.uuid == rule_uuid,
        ).update(update_rule)

        self.session.commit()
        return

    def get_rule_rely(self, rule_category_id: int) -> model.RuleResponseRely:
        rule_rely = orm.rule_details_rely

        query = select(
            rule_rely.c.id,
            rule_rely.c.data_volume,
            rule_rely.c.data_unit,
            rule_rely.c.sms_unit,
            rule_rely.c.voice_unit,
            rule_rely.c.threshold,
            rule_rely.c.percentage_unit,
            rule_rely.c.view_deactivate_sim,
            rule_rely.c.required_deactivate_sim,
            rule_rely.c.view_rate_plan_change,
            rule_rely.c.required_rate_plan_change,
            rule_rely.c.add_any_rate_plan,
            rule_rely.c.is_monthly_pool,
            rule_rely.c.view_email,
            rule_rely.c.view_sms,
            rule_rely.c.view_push,
        ).filter(rule_rely.c.rule_category_id == rule_category_id)

        result = self.session.execute(query).first()
        if not result:
            raise RuleRelyError(
                f"Rule rely with rule_category_id={rule_category_id} does not exist."
            )
        return model.RuleResponseRely(
            id=result.id,
            rules=model.RulesRely(
                data_volume=result.data_volume,
                data_unit=result.data_unit,
                sms_unit=result.sms_unit,
                voice_unit=result.voice_unit,
                threshold=result.threshold,
                percentage_unit=result.percentage_unit,
            ),
            action=model.ActionRely(
                view_deactivate_sim=result.view_deactivate_sim,
                required_deactivate_sim=result.required_deactivate_sim,
                view_rate_plan_change=result.view_rate_plan_change,
                required_rate_plan_change=result.required_rate_plan_change,
                add_any_rate_plan=result.add_any_rate_plan,
                is_monthly_pool=result.is_monthly_pool,
            ),
            notification=model.NotificationRely(
                view_email=result.view_email,
                view_sms=result.view_sms,
                view_push=result.view_push,
            ),
        )

    def create_rule_rely(self, rule_rely: model.RuleDetailsRely) -> bool:
        self.session.add(rule_rely)
        self.session.commit()
        return True

    def get_rule_rely_by_id(self, rule_rely_id: int):
        return (
            self.session.query(model.RuleDetailsRely).filter_by(id=rule_rely_id).first()
        )

    def update_rule_rely(self, rule_rely: model.RuleDetailsRely, rely_id: int) -> None:
        rely = orm.rule_details_rely
        update_rule_rely = {
            rely.c.rule_category_id: rule_rely.rule_category_id,
            rely.c.data_volume: rule_rely.data_volume,
            rely.c.data_unit: rule_rely.data_unit,
            rely.c.sms_unit: rule_rely.sms_unit,
            rely.c.voice_unit: rule_rely.voice_unit,
            rely.c.threshold: rule_rely.threshold,
            rely.c.percentage_unit: rule_rely.percentage_unit,
            rely.c.view_deactivate_sim: rule_rely.view_deactivate_sim,
            rely.c.required_deactivate_sim: rule_rely.required_deactivate_sim,
            rely.c.view_rate_plan_change: rule_rely.view_rate_plan_change,
            rely.c.required_rate_plan_change: rule_rely.required_rate_plan_change,
            rely.c.add_any_rate_plan: rule_rely.add_any_rate_plan,
            rely.c.is_monthly_pool: rule_rely.is_monthly_pool,
            rely.c.view_email: rule_rely.view_email,
            rely.c.view_sms: rule_rely.view_sms,
            rely.c.view_push: rule_rely.view_push,
        }
        self.session.query(rely).filter(
            rely.c.id == rely_id,
        ).update(update_rule_rely)
        self.session.commit()

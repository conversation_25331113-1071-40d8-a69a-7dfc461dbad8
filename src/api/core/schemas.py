from datetime import date
from enum import Enum

from pydantic import Field, root_validator

from api.schema_types import CamelBaseModel
from common.types import Month
from core.domain.model import CreatePartitions


class Partitions(str, Enum):
    cdr = "cdr"
    cdr_data = "cdr_data"
    cdr_voice = "cdr_voice"
    cdr_sms = "cdr_sms"
    cdr_data_aggregate = "cdr_data_aggregate"
    cdr_voice_aggregate = "cdr_voice_aggregate"


class PartitionsList(CamelBaseModel):
    month: Month | None = Field(None, description="Month in YYYY-MM format or null")
    partitions: list[Partitions] = Field(min_items=1)

    @root_validator(pre=True)
    def set_default_month(cls, values):
        if values.get("month") is None:
            today = date.today()
            next_month = Month(year=today.year, month=today.month, day=1).next()
            values["month"] = next_month
        return values

    class Config:
        schema_extra = {
            "example": {
                "month": "2022-11",
                "partitions": [
                    "cdr",
                    "cdr_data",
                    "cdr_voice",
                    "cdr_sms",
                    "cdr_data_aggregate",
                    "cdr_voice_aggregate",
                ],
            }
        }

    def to_model(self) -> CreatePartitions:
        if self.month is None:
            raise ValueError("Month cannot be None")

        return CreatePartitions(
            month=self.month.strftime("%Y_%m"),
            partitions=[partition.value for partition in self.partitions],
        )

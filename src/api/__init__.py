"""Public API."""
from fastapi import APIRouter, Depends

import api.accounts.endpoints as accounts
import api.audit.endpoints as audit
import api.authorization.endpoints as authorization_endpoint
import api.automation.endpoints as automation
import api.billing.endpoints as billing
import api.cdrdata.endpoints as cdrdata
import api.core.endpoints as core
import api.openapi.endpoints as openapi
import api.rate_plans.endpoints as rate_plans
import api.rating.endpoints as rating
import api.sim.endpoints as sim_endpoint
import api.sim.market_share_endpoints as market_Share
import api.sim.monitoring_endpoints as sim_monitoring_endpoint
import api.sim.notification_endpoint as notification_endpoint
from api import deps

router = APIRouter(dependencies=[Depends(deps.get_authenticated_actor)])
router.include_router(rating.router)
router.include_router(rate_plans.router)
router.include_router(billing.router)
router.include_router(sim_endpoint.router)
router.include_router(accounts.router)
router.include_router(audit.router)
router.include_router(market_Share.marketShare)
router.include_router(authorization_endpoint.router)
router.include_router(cdrdata.cdrauth_router)
router.include_router(openapi.token_router)
router.include_router(automation.router)
router.include_router(sim_monitoring_endpoint.router)
router.include_router(core.router)

cdrrouter = APIRouter()
cdrrouter.include_router(cdrdata.router)

notification_router = APIRouter()
notification_router.include_router(notification_endpoint.notification)

openapirouter = APIRouter()
openapirouter.include_router(openapi.router)

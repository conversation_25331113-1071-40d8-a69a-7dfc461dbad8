from itertools import starmap
from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request, status

import api.deps as trace_id_deps
from accounts.domain.dto import Account
from api.authorization.deps import authorization_service
from api.automation import deps, schemas
from api.automation.schemas import (
    Actions,
    CreateRuleDetails,
    CreateRuleRequest,
    Notifications,
    RuleCategory,
    RuleData,
    RuleDefinition,
    RuleDetails,
    RuleRequestRely,
    RuleResponseRely,
    Rules,
    RulesData,
    RuleType,
    UpdateRuleRequest,
)
from api.decorators import check_permissions
from api.deps import redis_service
from api.rate_plans.deps import rate_plan_service
from api.resolvers import ResolveError, Resolver
from api.schema_types import PaginatedResponse
from app.config import logger
from app.constants import LOCK_STATUS_PATCH_API
from auth.exceptions import ForbiddenError
from authorization.domain.ports import AbstractAuthorizationAPI
from automation.adapters.exceptions import RuleNotFound
from automation.domain.model import ActionValue, ChangeRatePlanValue
from automation.exceptions import (
    <PERSON>reate<PERSON>uleError,
    DeleteRuleError,
    NoRuleCategory,
    NoRuleDefinition,
    NoRuleTypeFound,
    RuleAlreadyExist,
    RuleCategoryError,
    RuleDetailsNotFound,
    RuleError,
    RuleRelyConflictError,
    RuleRelyError,
    UpdateLockRuleError,
    UpdateRuleError,
)
from automation.services import AutomationService
from common.ip_utils import get_client_ip
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import DataUnit, EnumerationList, PercentageUnit, SMSUnit, VoiceUnit
from rate_plans.exceptions import RatePlanDoesNotExist, RatePlanError
from rate_plans.services import RatePlanService
from redis.exception import RedisAPIError
from redis.service import RedisService

router = APIRouter(tags=["automation"], prefix="/rule")


@router.get("/rule-unit")
def get_rule_unit_enum(
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    enum_map = {
        "dataUnit": EnumerationList.from_enum(DataUnit),
        "voiceUnit": EnumerationList.from_enum(VoiceUnit),
        "smsUnit": EnumerationList.from_enum(SMSUnit),
        "percentageUnit": EnumerationList.from_enum(PercentageUnit),
    }
    return enum_map


@router.get("/rule-action")
def get_rule_action_enum(
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    enum_map = {
        "reactivateSim": EnumerationList.from_enum(ActionValue),
        "changeRatePlan": EnumerationList.from_enum(ChangeRatePlanValue),
    }
    return enum_map


@router.get(
    "/category",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[RuleCategory],
)
def get_rule_category(
    automation_service: AutomationService = Depends(deps.automation_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            RuleCategory,
            default_ordering="id",
            ordering_fields=(
                "id",
                "category",
                "rule_type",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "category",
            },
        )
    ),
    type_id: int | None = None,
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[RuleCategory]:
    try:
        records, total_count = automation_service.get_rule_category(
            type_id=type_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=map(RuleCategory.from_model, records),
            total_count=total_count,
        )
    except NoRuleCategory as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.post(
    "",
    status_code=status.HTTP_201_CREATED,
    response_model=CreateRuleDetails,
)
@check_permissions
def create_rule(
    request: Request,
    rule_request: CreateRuleRequest,
    background_tasks: BackgroundTasks,
    redis_service: RedisService = Depends(redis_service),
    rate_plan_service: RatePlanService = Depends(rate_plan_service),
    automation_service: AutomationService = Depends(deps.automation_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> CreateRuleDetails:
    try:
        client_ip = get_client_ip(request)

        rule_request.ip_address = client_ip

        rules_detail = rule_request.to_model()
        automation_service.validate_rate_plan(rate_plan_service, rules_detail)
        response = automation_service.create_rule(
            rules_detail=rules_detail,
            scope=LOCK_STATUS_PATCH_API,
            authorization=authorization,
        )
        background_tasks.add_task(
            redis_service.create_redis_rule, rule_request.account_id
        )
        return CreateRuleDetails.from_model(rule_details=response)
    except RatePlanError as e:
        logger.error(f"Rule Rate Plan Error:- {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            f"{str(e)}",
        )
    except RuleAlreadyExist as e:
        logger.error(f"Database Integrity error occoured.: {e}")
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            detail=str(e),
        )
    except RuleCategoryError as e:
        logger.error(f"Rule Category Error:- {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            "Rule Category not found",
        )
    except (CreateRuleError, RuleError) as e:
        logger.error(f"Database Integrity error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while creating rule.",
        )
    except UpdateLockRuleError as e:
        logger.error(f"Lock Rule error: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            f"{str(e)}",
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden error occoured.: {e}")
        raise HTTPException(
            status.HTTP_403_FORBIDDEN,
            "You are trying to access a restricted area.",
        )
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except RedisAPIError as e:
        logger.error(f"Error in create_redis_rule. Error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while creating rule.",
        )
    except (RatePlanDoesNotExist, AssertionError) as e:
        logger.error(f"Invalid rate plan. Error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "Invalid rate plan.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.put(
    "",
    status_code=status.HTTP_201_CREATED,
    response_model=RuleDetails,
)
@check_permissions
def update_rule(
    request: Request,
    rules_uuid: UUID,
    rule_request: UpdateRuleRequest,
    background_tasks: BackgroundTasks,
    redis_service: RedisService = Depends(redis_service),
    rate_plan_service: RatePlanService = Depends(rate_plan_service),
    automation_service: AutomationService = Depends(deps.automation_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> RuleDetails:
    try:
        client_ip = get_client_ip(request)

        rule_request.ip_address = client_ip

        rules_detail = rule_request.update_rule_to_model(uuid=rules_uuid)
        automation_service.validate_rate_plan(rate_plan_service, [rules_detail])
        response = automation_service.update_rule(
            rules_uuid=rules_uuid,
            rules_detail=rules_detail,
            scope=LOCK_STATUS_PATCH_API,
            authorization=authorization,
        )
        background_tasks.add_task(
            redis_service.update_redis_rule,
            account_id=rule_request.account_id,
            rule_uuid=rules_uuid,
        )
        return RuleDetails.from_model(rule_details=response)
    except RatePlanError as e:
        logger.error(f"Rule Rate Plan Error:- {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            f"{str(e)}",
        )
    except RuleCategoryError as e:
        logger.error(f"Rule Category Error:- {str(e)}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            "Rule Category not found",
        )
    except RuleAlreadyExist as e:
        logger.error(f"Database Integrity error occoured.: {e}")
        raise HTTPException(
            status.HTTP_409_CONFLICT,
            detail=str(e),
        )
    except UpdateRuleError as e:
        logger.error(f"Update rule error occoured.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            f"{str(e)}",
        )
    except UpdateLockRuleError as e:
        logger.error(f"Update lock rule status error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            str(e),
        )
    except RuleError as e:
        logger.error(f"Database Integrity error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while updating rule.",
        )
    except RuleNotFound as e:
        logger.error(f"Rule not found: {e}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Rule not found.",
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden error occoured.: {e}")
        raise HTTPException(
            status.HTTP_403_FORBIDDEN,
            "You are trying to access a restricted area.",
        )
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except RedisAPIError as e:
        logger.error(f"Error in update_redis_rule. Error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while updating rule.",
        )
    except (RatePlanDoesNotExist, AssertionError) as e:
        logger.error(f"Invalid rate plan. Error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "Invalid rate plan.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


def get_action(
    automation_service: AutomationService = Depends(deps.automation_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            Actions,
            default_ordering="id",
            ordering_fields=(
                "id",
                "action",
            ),
        )
    ),
    action_id: int | None = None,
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "action",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[Actions]:
    actions = automation_service.get_actions(
        action_id=action_id,
        pagination=pagination,
        ordering=ordering,
        searching=searching,
    )

    total_count = automation_service.get_actions_count(
        action_id=action_id, searching=searching
    )
    if total_count == 0:
        raise HTTPException(status_code=404, detail="No action found")

    result_data = list(map(Actions.from_model, actions))

    if result_data is None or len(result_data) == 0:
        raise HTTPException(status_code=404, detail="No action found")

    return PaginatedResponse.from_iterable(
        pagination=pagination,
        results=result_data,
        total_count=total_count,
    )


def get_notification(
    automation_service: AutomationService = Depends(deps.automation_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            Notifications,
            default_ordering="id",
            ordering_fields=(
                "id",
                "notification",
            ),
        )
    ),
    notification_id: int | None = None,
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "notification",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[Notifications]:
    notifications = automation_service.get_notification(
        notification_id=notification_id,
        pagination=pagination,
        ordering=ordering,
        searching=searching,
    )

    total_count = automation_service.get_notification_count(
        notification_id=notification_id, searching=searching
    )

    if total_count == 0:
        raise HTTPException(status_code=404, detail="No notification found")

    notification_list = list(map(Notifications.from_model, notifications))

    if notification_list is None or len(notification_list) == 0:
        raise HTTPException(status_code=404, detail="No notification found")

    return PaginatedResponse.from_iterable(
        pagination=pagination,
        results=notification_list,
        total_count=total_count,
    )


router.get(
    "/action", status_code=status.HTTP_200_OK, response_model=PaginatedResponse[Actions]
)(get_action)

router.get(
    "/notification",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[Notifications],
)(get_notification)


@router.get(
    "/type",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[RuleType],
)
def get_rule_type(
    automation_service: AutomationService = Depends(deps.automation_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            RuleType,
            default_ordering="id",
            ordering_fields=(
                "id",
                "rule_type",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "id",
                "rule_type",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[RuleType]:
    try:
        rule_type, records = automation_service.get_rule_type(
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=list(map(RuleType.from_model, rule_type)),
            total_count=records,
        )

    except NoRuleTypeFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get(
    "/{rule_uuid}", response_model=schemas.RuleData, status_code=status.HTTP_200_OK
)
@check_permissions
def get_rule_by_uuid(
    request: Request,
    rule_uuid: UUID,
    automation_service: AutomationService = Depends(deps.automation_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> RuleData:
    "Fetch rule details by rule uuid"
    try:
        rule_detail = automation_service.get_rule_by_uuid(rule_uuid)
        get_account = account_resolver([rule_detail.account_id])
        account = get_account(rule_detail.account_id)

        response = schemas.RuleData.from_model_rule(
            rule_detail, account.name, account.id, account.logo_key, account.logo_url
        )
        return response
    except RuleNotFound as e:
        logger.error(f"Rule not found: {e}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except ResolveError:
        logger.error(f"Account with id {rule_detail.account_id} not found.")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Account id not found while getting rule details.",
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden error occoured.: {e}")
        raise HTTPException(
            status.HTTP_403_FORBIDDEN,
            "You are trying to access a restricted area.",
        )
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get(
    "",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[RulesData],
)
@check_permissions
def get_rule(
    request: Request,
    account_id: int | None = None,
    automation_service: AutomationService = Depends(deps.automation_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    ordering: Ordering = Depends(
        Ordering.query(
            Rules,
            default_ordering="-id",
            ordering_fields=(
                "id",
                "uuid",
                "account_id",
                "rule_type",
                "rule_category",
                "rule_definition",
                "data_volume",
                "unit",
                "status",
                "rule_name",
                "account_name",
                "lock",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "unit",
                "rule_name",
                "data_volume",
                "rule_definition",
                "rule_type",
                "rule_category",
                "account_name",
            },
        )
    ),
    pagination: Pagination = Depends(Pagination.query()),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[RulesData]:
    try:
        rule_detail, rules_count = automation_service.get_rule(
            account_id=account_id,
            ordering=ordering,
            searching=searching,
            pagination=pagination,
        )

        get_account = account_resolver(
            (rule_data.account_id for rule_data in rule_detail.result)
        )
        rule_detail_accounts = (
            (rule_data, get_account(rule_data.account_id))
            for rule_data in rule_detail.result
        )
        results = list(starmap(RulesData.from_model_rules, rule_detail_accounts))

        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=results,
            total_count=rules_count,
        )
    except RuleDetailsNotFound as e:
        logger.error(f"Rule not found error occoured.: {e}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            "No rules found.",
        )
    except ResolveError as e:
        logger.error(f"Account id not found while getting rule details.: {e}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Account id not found while getting rule details.",
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden error occoured.: {e}")
        raise HTTPException(
            status.HTTP_403_FORBIDDEN,
            "You are trying to access a restricted area.",
        )
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get(
    "/{rule_category_id}/definition",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[RuleDefinition],
)
def get_rule_definition(
    rule_category_id: int,
    automation_service: AutomationService = Depends(deps.automation_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            RuleDefinition,
            default_ordering="id",
            ordering_fields=("id", "definition"),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={"id", "definition"},
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[RuleDefinition]:
    try:
        records, total_count = automation_service.get_rule_definition(
            rule_category_id=rule_category_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=map(RuleDefinition.from_model, records),
            total_count=total_count,
        )
    except NoRuleDefinition as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.patch(
    "/{rule_uuid}/{rule_status}",
    response_model=schemas.RuleStatus,
    status_code=status.HTTP_200_OK,
)
@check_permissions
def update_rule_status_by_rule_uuid(
    request: Request,
    rule_uuid: UUID,
    rule_status: bool,
    background_tasks: BackgroundTasks,
    redis_service: RedisService = Depends(redis_service),
    automation_service: AutomationService = Depends(deps.automation_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> schemas.RuleStatus:
    "Patch rule status by rule uuid"
    try:
        rule_detail = automation_service.update_rule_status_by_rule_uuid(
            rule_uuid=rule_uuid, rule_status=rule_status
        )
        background_tasks.add_task(
            redis_service.update_rule_status,
            rule_status,
            rule_uuid=rule_uuid,
        )
        return schemas.RuleStatus.from_model(rule_detail)
    except UpdateRuleError as e:
        logger.error(f"Update rule status error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            f"Update rule status error.: {str(e)}",
        )
    except UpdateLockRuleError as e:
        logger.error(f"Update lock rule status error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            str(e),
        )
    except RuleNotFound as e:
        logger.error(f"Rule not found: {e}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Rule not found.",
        )
    except RuleError as e:
        logger.error(f"Database Integrity error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while updating rule.",
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden error occoured.: {e}")
        raise HTTPException(
            status.HTTP_403_FORBIDDEN,
            "You are trying to access a restricted area.",
        )
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except RedisAPIError as e:
        logger.error(f"Error in update_redis_rule. Error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while updating rule.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.delete(
    "",
    status_code=status.HTTP_204_NO_CONTENT,
)
def delete_rule(
    rules_uuid: UUID,
    background_tasks: BackgroundTasks,
    redis_service: RedisService = Depends(redis_service),
    automation_service: AutomationService = Depends(deps.automation_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> None:
    try:
        automation_service.delete_rule(rules_uuid=rules_uuid)
        background_tasks.add_task(
            redis_service.delete_redis_rule,
            rule_uuid=rules_uuid,
        )
    except RuleError as e:
        logger.error(f"Database Integrity error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while deleting rule.",
        )
    except DeleteRuleError as e:
        logger.error(f"Delete rule error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            f"Delete rule error.: {str(e)}",
        )
    except RuleNotFound as e:
        logger.error(f"Rule not found: {e}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Rule not found.",
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden error occoured.: {e}")
        raise HTTPException(
            status.HTTP_403_FORBIDDEN,
            "You are trying to access a restricted area.",
        )
    except RedisAPIError as e:
        logger.error(f"Error in delete_redis_rule. Error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while deleting rule.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.patch(
    "/{rule_uuid}/lock/{lock_status}",
    response_model=schemas.LockStatus,
    status_code=status.HTTP_200_OK,
)
@check_permissions
def update_lock_status_by_rule_uuid(
    request: Request,
    rule_uuid: UUID,
    lock_status: bool,
    background_tasks: BackgroundTasks,
    redis_service: RedisService = Depends(redis_service),
    automation_service: AutomationService = Depends(deps.automation_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> schemas.LockStatus:
    "Patch rule lock status by rule uuid"
    try:
        rule_detail = automation_service.update_lock_status_by_rule_uuid(
            rule_uuid=rule_uuid, lock_status=lock_status
        )

        return schemas.LockStatus.from_model(rule_detail)
    except UpdateRuleError as e:
        logger.error(f"Update rule status error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            f"Update rule status error.: {str(e)}",
        )
    except RuleNotFound as e:
        logger.error(f"Rule not found: {e}")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Rule not found.",
        )
    except RuleError as e:
        logger.error(f"Database Integrity error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while updating rule.",
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden error occoured.: {e}")
        raise HTTPException(
            status.HTTP_403_FORBIDDEN,
            "You are trying to access a restricted area.",
        )
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except RedisAPIError as e:
        logger.error(f"Error in update_redis_rule. Error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "An error occoured while updating rule.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get(
    "/{rule_category_id}/rule-rely",
    status_code=status.HTTP_200_OK,
    response_model=RuleResponseRely,
)
def get_rule_rely(
    rule_category_id: int,
    automation_service: AutomationService = Depends(deps.automation_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> RuleResponseRely:
    try:
        records = automation_service.get_rule_rely(
            rule_category_id=rule_category_id,
        )
        return RuleResponseRely.from_model(records)
    except RuleRelyError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except NoRuleDefinition as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.post(
    "/rule-rely",
    status_code=status.HTTP_201_CREATED,
)
def create_rule_rely(
    rule_request: RuleRequestRely,
    automation_service: AutomationService = Depends(deps.automation_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> dict:
    try:
        rule_rely = rule_request.to_model(rule_request_rely=rule_request)
        automation_service.create_rule_rely(rule_rely=rule_rely)
        return {"status": "success"}
    except NoRuleCategory as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except RuleRelyError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except RuleRelyConflictError as e:
        raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail=str(e))
    except ForbiddenError as e:
        logger.error(f"Forbidden error occoured.: {e}")
        raise HTTPException(
            status.HTTP_403_FORBIDDEN,
            "You are trying to access a restricted area.",
        )
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.put(
    "/rule-rely/{rule_rely_id}",
    status_code=status.HTTP_200_OK,
)
def update_rule_rely(
    rule_rely_id: int,
    rule_request: RuleRequestRely,
    automation_service: AutomationService = Depends(deps.automation_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> dict:
    try:
        rely = rule_request.to_model(rule_request_rely=rule_request)
        automation_service.update_rule_rely(rely, rule_rely_id)
        return {"status": "success"}
    except NoRuleCategory as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except RuleRelyError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ForbiddenError as e:
        logger.error(f"Forbidden error occurred: {e}")
        raise HTTPException(
            status.HTTP_403_FORBIDDEN,
            "You are trying to access a restricted area.",
        )
    except ValueError as e:
        logger.error(f"Value error occurred: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except Exception as e:
        logger.error(f"Couldn't process the request: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )

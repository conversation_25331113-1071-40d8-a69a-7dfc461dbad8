from uuid import UUID, uuid4

from pydantic import AnyHttpUrl, EmailStr, Field, root_validator, validator

from accounts.domain.dto import Account
from api.automation.examples import (
    ACTIONS,
    LOCK,
    NOTIFICATION,
    NOTIFICATIONS,
    RULE_CATEGORY,
    <PERSON>U<PERSON>_DEFINITION,
    RULE_DETAIL,
    RULE_NOTIFICATION,
    RULE_REQUEST,
    RULE_REQUEST_RELY,
    RULE_RESPONSE,
    RULE_RESPONSE_RELY,
    RULE_STATUS,
    RULE_TYPE,
)
from api.schema_types import CamelBaseModel
from automation.domain import model
from common.types import MSISDN, RULE_NAME, Unit


class RuleDetails(CamelBaseModel):
    uuid: UUID

    class Config:
        schema_extra = {"example": RULE_RESPONSE}

    @classmethod
    def from_model(cls, rule_details: model.RuleDetails) -> "RuleDetails":
        return cls(uuid=rule_details.uuid)


class CreateRuleDetails(CamelBaseModel):
    uuids: list[RuleDetails]

    class Config:
        schema_extra = {"example": RULE_RESPONSE}

    @classmethod
    def from_model(cls, rule_details: model.CreateRuleDetails) -> "CreateRuleDetails":
        return cls(
            uuids=[
                RuleDetails.from_model(rule_detail)
                for rule_detail in rule_details.rules_uuid
            ]
        )


class deactivateAction(CamelBaseModel):
    action: model.RuleAction = Field(model.RuleAction.REACTIVATE_SIM, readOnly=True)
    value: str

    @validator("value")
    def validate_value(cls, v):
        if not v.strip():
            raise ValueError("value must not be empty or contain only whitespace.")
        return v


class ratePlanAction(CamelBaseModel):
    action: model.RuleAction = Field(model.RuleAction.CHANGE_RATE_PLAN, readOnly=True)
    source: int | None = Field(..., ge=1)
    target: int = Field(..., ge=1)
    value: model.ChangeRatePlanValue


class ActionDetail(CamelBaseModel):
    reactivateSim: deactivateAction | None = None
    ratePlan: ratePlanAction | None = None

    def to_model(self, uuid: UUID) -> list[model.RulesAction]:
        actions = []
        if self.reactivateSim:
            actions.append(
                model.RulesAction(
                    action=self.reactivateSim.action,
                    rules_uuid=uuid,
                    action_value=self.reactivateSim.value,
                )
            )
        if self.ratePlan:
            actions.append(
                model.RulesAction(
                    action=self.ratePlan.action,
                    rules_uuid=uuid,
                    action_value=self.ratePlan.value,
                    source=self.ratePlan.source,
                    target=self.ratePlan.target,
                )
            )
        return actions


class NotificationValue(CamelBaseModel):
    id: int
    rule_notification_id: int
    notification_value: list[EmailStr] | list[MSISDN]


class NotificationDetail(CamelBaseModel):
    id: int
    notification: bool
    notification_value: list[EmailStr] | list[MSISDN]

    class Config:
        schema_extra = {"example": RULE_NOTIFICATION}

    def to_model(self, uuid: UUID) -> model.RulesNotification:
        return model.RulesNotification(
            rules_uuid=uuid,
            notifications_id=self.id,
            notification=self.notification,
            notification_value=[
                model.NotificationValue(
                    notification_value=notification_value,
                )
                for notification_value in self.notification_value
            ],
        )


class RuleRequest(CamelBaseModel):
    uuid: UUID | None = Field(readOnly=True)
    created_by: str | None = Field(readOnly=True)
    ip_address: str | None = Field(readOnly=True)
    account_id: list[int] = Field(min_items=1)
    rule_type_id: int
    rule_category_id: int
    rule_definition_id: int
    rule_name: RULE_NAME
    data_volume: int
    unit: Unit
    status: bool
    action: ActionDetail
    notification: list[NotificationDetail]
    lock: bool

    class Config:
        schema_extra = {"example": RULE_REQUEST}

    @root_validator
    def check_action_or_notification(cls, values):
        action = values.get("action")
        notification = values.get("notification")
        if not action and not notification:
            raise ValueError("At least one of action or notification must be present")
        return values

    @validator("data_volume")
    def validate_data_volume(cls, value):
        if not isinstance(value, int):
            raise ValueError("Value must be an integer")
        if value < 1:
            raise ValueError("Value must be greater than 0")
        if len(str(abs(value))) > 15:
            raise ValueError("Value cannot have more than 15 digits")
        return value


class CreateRuleRequest(RuleRequest):
    class Config:
        schema_extra = {"example": RULE_REQUEST}

    @validator("account_id")
    def validate_account_id(cls, value):
        if len(value) != len(set(value)):
            raise ValueError("Account value must be unique")
        return value

    def to_model(
        self,
    ) -> list[model.Rules]:
        rules_details = list(
            map(
                lambda account_id: model.Rules(
                    uuid := uuid4(),
                    account_id=account_id,
                    created_by=self.created_by,
                    ip_address=self.ip_address,
                    rule_type_id=self.rule_type_id,
                    rule_category_id=self.rule_category_id,
                    rule_definition_id=self.rule_definition_id,
                    rule_name=self.rule_name,
                    data_volume=self.data_volume,
                    unit=self.unit,
                    status=self.status,
                    lock=self.lock,
                    actions=self.action.to_model(uuid) if self.action else None,
                    notifications=list(
                        notification.to_model(uuid=uuid)
                        for notification in self.notification
                    )
                    if self.notification
                    else None,
                ),
                self.account_id,
            )
        )
        return rules_details


class Actions(CamelBaseModel):
    id: int
    action: str

    class config:
        schema_extra = {"example": ACTIONS}
        orm_mode = True

    @classmethod
    def from_model(cls, sim_automation: model.Actions) -> "Actions":
        return cls(
            id=sim_automation.id,
            action=sim_automation.action,
        )


class Notifications(CamelBaseModel):
    id: int
    notification: str

    class Config:
        schema_extra = {"example": NOTIFICATIONS}
        orm_mode = True

    @classmethod
    def from_model(cls, sim_automation: model.Notifications) -> "Notifications":
        return cls(
            id=sim_automation.id,
            notification=sim_automation.notification,
        )


class Rules(CamelBaseModel):
    uuid: UUID
    account_id: int | None
    created_by: str | None
    ip_address: str | None
    rule_type_id: int
    rule_category_id: int
    rule_definition_id: int
    rule_name: RULE_NAME
    data_volume: int
    status: bool
    action: list[ActionDetail]
    notification: list[NotificationDetail]

    class Config:
        schema_extra = {"example": RULE_RESPONSE}

    @classmethod
    def from_model(cls, rule_details: model.Rules) -> "Rules":
        return cls(
            uuid=rule_details.uuid,
            account_id=rule_details.account_id,
            created_by=rule_details.created_by,
            ip_address=rule_details.ip_address,
            rule_type_id=rule_details.rule_type_id,
            rule_category_id=rule_details.rule_category_id,
            rule_definition_id=rule_details.rule_definition_id,
            rule_name=rule_details.rule_name,
            data_volume=rule_details.data_volume,
            status=rule_details.status,
            action=rule_details.actions,
            notification=rule_details.notifications,
        )


class deactivateActionResponse(CamelBaseModel):
    name: str
    action_value: str


class ratePlanActionResponse(CamelBaseModel):
    name: str
    action_value: str
    target: int
    source: int | None


class ActionDetailResponse(CamelBaseModel):
    reactivate_sim: deactivateActionResponse | None = None
    rate_plan: ratePlanActionResponse | None = None

    @classmethod
    def from_model(
        cls, actions: list[model.Action | model.RateplanAction]
    ) -> "ActionDetailResponse":
        reactivate_sim_action = None
        rate_plan_action = None

        for action in actions:
            if isinstance(action, model.Action):
                reactivate_sim_action = deactivateActionResponse(
                    name=action.name,
                    action_value=action.action_value,
                )
            elif isinstance(action, model.RateplanAction):
                rate_plan_action = ratePlanActionResponse(
                    name=action.name,
                    action_value=action.action_value,
                    source=action.source,
                    target=action.target,
                )

        return ActionDetailResponse(
            reactivate_sim=reactivate_sim_action, rate_plan=rate_plan_action
        )


class Notification(CamelBaseModel):
    name: str
    notification: bool
    notification_value: list[EmailStr] | list[MSISDN]

    class Config:
        schema_extra = {"example": NOTIFICATION}


class NotificationChannels(CamelBaseModel):
    email: list[EmailStr] | None = None
    sms: list[str] | None = None
    view_push: list[str] | None = None


class RuleDetail(CamelBaseModel):
    uuid: UUID
    account_name: str
    account_id: int
    rule_name: RULE_NAME
    data_volume: int
    unit: Unit
    status: bool
    lock: bool
    action: ActionDetailResponse
    notification: NotificationChannels
    logo_key: str | None = None
    logo_url: AnyHttpUrl | None = None

    class config:
        schema_extra = {"example": RULE_DETAIL}
        orm_mode = True

    @classmethod
    def channels(cls, rule_notification: list[Notification]) -> NotificationChannels:
        email: list[EmailStr | str] = []
        sms: list[str] = []
        push: list[str] = []
        for n in rule_notification:
            if n.name == "Send email":
                email.extend(n.notification_value)
            elif n.name == "Send SMS":
                sms.extend(n.notification_value)
            elif n.name == "Push":
                push.extend(n.notification_value)
        return NotificationChannels(email=email, sms=sms, push=push)


class RuleTypeDetails(CamelBaseModel):
    id: int
    rule_type: str

    @classmethod
    def from_model(cls, rule_type: model.RulesTypeDetails) -> "RuleTypeDetails":
        return cls(
            id=rule_type.id,
            rule_type=rule_type.rule_type,
        )


class RuleCategoryDetails(CamelBaseModel):
    id: int
    rule_category: str

    @classmethod
    def from_model(
        cls, rule_category: model.RulesCategoryDetails
    ) -> "RuleCategoryDetails":
        return cls(
            id=rule_category.id,
            rule_category=rule_category.rule_category,
        )


class RuleDefinitionDetails(CamelBaseModel):
    id: int
    rule_definition: str

    @classmethod
    def from_model(
        cls, rule_definition: model.RulesDefinitionDetails
    ) -> "RuleDefinitionDetails":
        return cls(
            id=rule_definition.id,
            rule_definition=rule_definition.rule_definition,
        )


class RuleData(RuleDetail):
    rule_type: RuleTypeDetails
    rule_category: RuleCategoryDetails
    rule_definition: RuleDefinitionDetails

    @classmethod
    def from_model_rule(
        cls,
        rule: model.RuleDetailsResponse,
        account_name: str,
        account_id: int,
        logo_key: str | None,
        logo_url: AnyHttpUrl | None,
    ) -> "RuleData":
        notification_channels = cls.channels(rule.notification)  # type: ignore
        return cls(
            uuid=rule.uuid,
            account_name=str(account_name),
            account_id=account_id,
            rule_type=RuleTypeDetails.from_model(rule.rule_type),
            rule_category=RuleCategoryDetails.from_model(rule.rule_category),
            rule_definition=RuleDefinitionDetails.from_model(rule.rule_definition),
            rule_name=rule.rule_name,
            data_volume=rule.data_volume,
            unit=rule.unit,
            status=rule.status,
            lock=rule.lock,
            notification=notification_channels,
            action=ActionDetailResponse.from_model(rule.action),
            logo_url=logo_url,
            logo_key=logo_key,
        )


class RulesData(RuleDetail):
    rule_type: str
    rule_category: str
    rule_definition: str

    @classmethod
    def from_model_rules(
        cls,
        rule: model.RuleDetailsResponse,
        account: Account,
    ) -> "RulesData":

        notification_channels = cls.channels(rule.notification)  # type: ignore
        return cls(
            uuid=rule.uuid,
            account_name=account.name,
            account_id=account.id,
            rule_type=rule.rule_type,
            rule_category=rule.rule_category,
            rule_name=rule.rule_name,
            rule_definition=rule.rule_definition,
            data_volume=rule.data_volume,
            unit=rule.unit,
            status=rule.status,
            lock=rule.lock,
            notification=notification_channels,
            action=ActionDetailResponse.from_model(rule.action),
            logo_url=account.logo_url,
            logo_key=account.logo_key,
        )


class RuleType(CamelBaseModel):
    id: int
    rule_type: str

    class Config:
        schema_extra = {"example": RULE_TYPE}
        orm_mode = True

    @classmethod
    def from_model(cls, rule_type: model.RuleType) -> "RuleType":
        return cls(
            id=rule_type.id,
            rule_type=rule_type.rule_type,
        )


class RuleCategory(CamelBaseModel):
    id: int
    rule_type_id: int
    rule_type: str
    category: str

    class Config:
        schema_extra = {"example": RULE_CATEGORY}
        orm_mode = True

    @classmethod
    def from_model(cls, rule_category: model.RuleCategoryDetails) -> "RuleCategory":
        return cls(
            id=rule_category.id,
            rule_type_id=rule_category.rule_type_id,
            rule_type=rule_category.rule_type,
            category=rule_category.category,
        )


class RuleDefinition(CamelBaseModel):
    id: int
    definition: str
    category: str

    class Config:
        schema_extra = {"example": RULE_DEFINITION}
        orm_mode = True

    @classmethod
    def from_model(
        cls, rule_definition: model.RuleDefinitionDetails
    ) -> "RuleDefinition":
        return cls(
            id=rule_definition.id,
            definition=rule_definition.definition,
            category=rule_definition.category,
        )


class UpdateRuleRequest(RuleRequest):
    class Config:
        schema_extra = {"example": RULE_REQUEST}

    @validator("account_id")
    def validate_account_id(cls, value):
        if len(value) != 1:
            raise ValueError("Account value must contain exactly one item.")
        return value

    def update_rule_to_model(self, uuid: UUID) -> model.Rules:
        return model.Rules(
            uuid=uuid,
            account_id=self.account_id[0],
            created_by=self.created_by,
            ip_address=self.ip_address,
            rule_type_id=self.rule_type_id,
            rule_category_id=self.rule_category_id,
            rule_definition_id=self.rule_definition_id,
            rule_name=self.rule_name,
            data_volume=self.data_volume,
            unit=self.unit,
            status=self.status,
            lock=self.lock,
            actions=self.action.to_model(uuid) if self.action else None,
            notifications=list(
                notification.to_model(uuid=uuid) for notification in self.notification
            )
            if self.notification
            else None,
        )


class RuleStatus(CamelBaseModel):
    uuid: UUID
    status: bool

    class Config:
        schema_extra = {"example": RULE_STATUS}

    @classmethod
    def from_model(cls, rule_status: model.RuleStatus) -> "RuleStatus":
        return cls(
            uuid=rule_status.uuid,
            status=rule_status.status,
        )


class LockStatus(CamelBaseModel):
    uuid: UUID
    lock: bool

    class Config:
        schema_extra = {"example": LOCK}

    @classmethod
    def from_model(cls, lock_status: model.LockStatus) -> "LockStatus":
        return cls(
            uuid=lock_status.uuid,
            lock=lock_status.lock,
        )


class RuleRely(CamelBaseModel):
    data_volume: bool
    data_unit: bool
    sms_unit: bool
    voice_unit: bool
    threshold: bool
    percentage_unit: bool

    @classmethod
    def from_model(cls, rule_rely: model.RulesRely) -> "RuleRely":
        return cls(
            data_volume=rule_rely.data_volume,
            data_unit=rule_rely.data_unit,
            sms_unit=rule_rely.sms_unit,
            voice_unit=rule_rely.voice_unit,
            threshold=rule_rely.threshold,
            percentage_unit=rule_rely.percentage_unit,
        )


class ActionRely(CamelBaseModel):
    view_deactivate_sim: bool
    required_deactivate_sim: bool
    view_rate_plan_change: bool
    required_rate_plan_change: bool
    add_any_rate_plan: bool
    is_monthly_pool: bool

    @classmethod
    def from_model(cls, action_rely: model.ActionRely) -> "ActionRely":
        return cls(
            view_deactivate_sim=action_rely.view_deactivate_sim,
            required_deactivate_sim=action_rely.required_deactivate_sim,
            view_rate_plan_change=action_rely.view_rate_plan_change,
            required_rate_plan_change=action_rely.required_rate_plan_change,
            add_any_rate_plan=action_rely.add_any_rate_plan,
            is_monthly_pool=action_rely.is_monthly_pool,
        )


class NotificationRely(CamelBaseModel):
    view_email: bool
    view_sms: bool
    view_push: bool

    @classmethod
    def from_model(
        cls, notification_rely: model.NotificationRely
    ) -> "NotificationRely":
        return cls(
            view_email=notification_rely.view_email,
            view_sms=notification_rely.view_sms,
            view_push=notification_rely.view_push,
        )


class RuleResponseRely(CamelBaseModel):
    id: int
    rules: RuleRely
    action: ActionRely
    notification: NotificationRely

    class Config:
        schema_extra = {"example": RULE_RESPONSE_RELY}
        orm_mode = True

    @classmethod
    def from_model(
        cls, rule_response_rely: model.RuleResponseRely
    ) -> "RuleResponseRely":
        return cls(
            id=rule_response_rely.id,
            rules=RuleRely.from_model(rule_rely=rule_response_rely.rules),
            action=ActionRely.from_model(action_rely=rule_response_rely.action),
            notification=NotificationRely.from_model(
                notification_rely=rule_response_rely.notification
            ),
        )


class RuleRequestRely(CamelBaseModel):
    rule_category_id: int
    data_volume: bool
    data_unit: bool
    sms_unit: bool
    voice_unit: bool
    threshold: bool
    percentage_unit: bool
    view_deactivate_sim: bool
    required_deactivate_sim: bool
    view_rate_plan_change: bool
    required_rate_plan_change: bool
    add_any_rate_plan: bool
    is_monthly_pool: bool
    view_email: bool
    view_sms: bool
    view_push: bool

    class Config:
        schema_extra = {"example": RULE_REQUEST_RELY}
        orm_mode = True

    @classmethod
    def to_model(cls, rule_request_rely) -> model.RuleDetailsRely:
        return model.RuleDetailsRely(
            rule_category_id=rule_request_rely.rule_category_id,
            data_volume=rule_request_rely.data_volume,
            data_unit=rule_request_rely.data_unit,
            sms_unit=rule_request_rely.sms_unit,
            voice_unit=rule_request_rely.voice_unit,
            threshold=rule_request_rely.threshold,
            percentage_unit=rule_request_rely.percentage_unit,
            view_deactivate_sim=rule_request_rely.view_deactivate_sim,
            required_deactivate_sim=rule_request_rely.required_deactivate_sim,
            view_rate_plan_change=rule_request_rely.view_rate_plan_change,
            required_rate_plan_change=rule_request_rely.required_rate_plan_change,
            add_any_rate_plan=rule_request_rely.add_any_rate_plan,
            is_monthly_pool=rule_request_rely.is_monthly_pool,
            view_email=rule_request_rely.view_email,
            view_sms=rule_request_rely.view_sms,
            view_push=rule_request_rely.view_push,
        )

"""msisdn and imsi indexes on sim card table

Revision ID: d9d647aa3f88
Revises: 0ad72e338357
Create Date: 2025-04-30 15:29:43.968689

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "d9d647aa3f88"
down_revision = "0ad72e338357"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
    CREATE INDEX IF NOT EXISTS idx_simcard_msisdn ON sim_card (msisdn);
    """
    )

    op.execute(
        """
    CREATE INDEX IF NOT EXISTS idx_simcard_imsi ON sim_card (imsi);
    """
    )


def downgrade() -> None:
    op.execute("drop index if exists idx_simcard_msisdn")
    op.execute("drop index if exists idx_simcard_imsi")

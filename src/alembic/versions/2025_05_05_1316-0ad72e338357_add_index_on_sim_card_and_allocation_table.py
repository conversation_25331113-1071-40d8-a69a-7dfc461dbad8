"""add index on sim card table

Revision ID: 0ad72e338357
Revises: 0cf658516c83
Create Date: 2025-05-05 13:16:48.595072

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "0ad72e338357"
down_revision = "0cf658516c83"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
       CREATE INDEX IF NOT EXISTS idx_allocation_imsi ON allocation (imsi);
       """
    )


def downgrade() -> None:
    op.execute("drop index if exists idx_allocation_imsi")

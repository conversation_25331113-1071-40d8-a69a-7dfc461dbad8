from typing import Any, Dict, Iterator
from uuid import UUID

from fastapi import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, status
from platform_api_client import PlatformAP<PERSON>lient, PlatformAPIError
from pydantic import EmailStr

from accounts.domain.ports import AbstractAccountRepository
from api.authorization.examples import GROUP_LIST, GROUP_ROLE_RESPONSE, PERMISSIONS
from app.config import logger, settings
from auth.exceptions import AuthException, ForbiddenError, NotFound
from authorization.adapters.repository import AbstractAuthRepository
from authorization.domain import model
from authorization.domain.ports import AbstractAuthorizationAPI
from authorization.exceptions import (
    GroupNotFound,
    PolicyAlreadyExist,
    PolicyNotFound,
    RoleDeletionError,
    RoleNotFound,
)
from common.pagination import Pagination
from common.searching import Searching


class FakeAuthorizationAPI(AbstractAuthorizationAPI):
    def __init__(self):
        self.users_scopes = {}

    def group_list(self) -> dict:
        return GROUP_LIST

    def get_id_by_name(self, group_name: str, group_list: dict) -> str | None:
        def find_group_id(data, group_name):
            for item in data:
                if item.get("name") == group_name:
                    return item.get("id")
                if "subGroups" in item:
                    sub_group_id = find_group_id(item["subGroups"], group_name)
                    if sub_group_id:
                        return sub_group_id
            return None

        return find_group_id(group_list["result"], group_name)

    def get_group_role(
        self,
        organization_id: int,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.GroupRole], int]:
        groups = self.group_list()
        if not groups:
            logger.error(f"Group with id {organization_id} not found")
            raise GroupNotFound(organization_id)
        self.get_id_by_name("bt-push-cdr-account", groups)
        response = [
            model.GroupRole(
                id=UUID("8b376afc-9014-49ff-ad1f-4589e122deda"),
                name="admin",
                description="admin",
                group="My Organization",
                permissions=0,
                userCount=0,
                createdBy="admin",
            )
        ]
        return response, 1  # type: ignore

    def get_permissions(
        self,
        role_id: UUID,
        organization_id: int | None = None,
    ) -> model.PermissionsResponse:
        return model.PermissionsResponse(
            id=UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
            name="ClientAdmin",
            roleGroup="My Organization",
            description="ClientAdmin description",
            permission=[
                "9cffe4ef-573a-4a03-8674-6b7d76265503",
                "d54db5ed-5c9e-416f-97f0-f178b01fe8d8",
            ],
        )

    def get_roles(
        self,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.GroupRole], int]:
        return (
            model.GroupRole(
                id=role.id,
                name=role.name,
                description=role.description,
                group=role.group,
                permissions=role.permissions,
                userCount=role.userCount,
                createdBy=role.createdBy,
            )
            for role in GROUP_ROLE_RESPONSE
        ), 1

    def create_role(
        self,
        role_request: model.RoleRequest,
        organization_id: int | None = None,
        created_by: EmailStr | None = None,
    ) -> model.RoleResponse:
        return model.RoleResponse(
            id=UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
            name=role_request.name,
            description=role_request.description,
            composite=False,
            clientRole=True,
            containerId="df279b03-0bcf-4ab5-9059-a72b2d2321a5",
            userCount=0,
        )

    def delete_role(self, role_uuid: UUID):
        return None

    def is_authorized(self, scope: str):
        return PERMISSIONS

    def get_user_scope(self) -> model.UserScopeList:
        return model.UserScopeList(result=[])


class HTTPAuthorizationAPI(AbstractAuthorizationAPI):
    GET_USERS_SCOPES = "/v1/authorization/user/evaluate"
    GET_GROUP_MEMBERS = "/v1/authorization/group/{group_id}/member"
    GET_GROUP_ROLE = "/v1/authorization/group/{group_id}/role"
    GROUP_LIST = "/v1/authorization/group"
    GROUP_ROLE_MAP = "/v1/authorization/group/{group_id}/roles-mapping"
    RESOURCE_SCOPES = "/v1/authorization/resource/scope/permission"
    GET_ROLE_BY_ID = "/v1/authorization/role/{role_id}"
    GET_POLICIES = "/v1/authorization/policy"
    GET_PERMISSIONS_BY_POLICY = "/v1/authorization/policy/{policy_id}/permission"
    GET_ROLES = "/v1/authorization/role"
    GET_PERMISSIONS = "/v1/authorization/user/scope"
    GET_AUTHORIZED_SCOPE = "/v1/authorization/user/evaluate/scope"
    GET_USER_SCOPE = "/v1/authorization/user/scope"

    def __init__(
        self,
        api_client: PlatformAPIClient,
        account_repository: AbstractAccountRepository,
        auth_repository: AbstractAuthRepository,
    ) -> None:
        self.api_client = api_client
        self.accounts = account_repository
        self.auth_repository = auth_repository

    def build_url(
        self, path: str, pagination: Pagination | None = None, search: str | None = None
    ) -> str:
        if not settings.APP_BASE_URL:
            logger.error("APP_BASE_URL is missing.")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="APP_BASE_URL is missing.",
            )
        url = settings.APP_BASE_URL + path
        if pagination and search:
            url = (
                settings.APP_BASE_URL
                + path
                + f"?page={pagination.page}&page_size={pagination.page_size}"
                + f"&search={search}"
            )
        elif pagination:
            url = (
                settings.APP_BASE_URL
                + path
                + f"?page={pagination.page}&page_size={pagination.page_size}"
            )
        elif search:
            url = settings.APP_BASE_URL + path + f"?search={search}"
        return url

    def group_list(self) -> dict:
        logger.info("In group_list.....")

        url = self.build_url(self.GROUP_LIST)
        logger.info(f"group_list-URL:-{str(url)}")
        group_list = self.api_client.get(url).json()
        return group_list

    def get_id_by_name(self, group_name: str, group_list: dict) -> str | None:
        logger.info("In get_id_by_name.....")

        def find_group_id(data, group_name):
            for item in data:
                if item.get("name") == group_name:
                    return item.get("id")
                if "subGroups" in item:
                    sub_group_id = find_group_id(item["subGroups"], group_name)
                    if sub_group_id:
                        return sub_group_id
            return None

        return find_group_id(group_list["results"], group_name)

    def get_group_role(
        self,
        organization_id: int,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.GroupRole], int]:
        try:
            logger.info("In get_group_role.....")

            url = self.build_url(self.GET_ROLES)
            logger.info(f"get_roles-URL:-{str(url)}")
            group_roles = self.api_client.get(url).json()["result"]
            db_role = self.auth_repository.get_roles_by_organization_id(
                organization_id, searching, pagination
            )
            roles = self._map_role_data(group_roles, db_role)

            total_count = len(
                self.auth_repository.get_roles_by_organization_id(
                    organization_id, searching
                )
            )
            return roles, total_count  # type: ignore
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound()
                case _:
                    raise e

    def _make_header(self, **kwargs: Any) -> Dict[str, Any]:
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json",
            **kwargs,
        }
        return headers

    def get_roles(
        self,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.GroupRole], int]:
        try:
            logger.info("In get_roles.....")
            url = self.build_url(self.GET_ROLES)
            logger.info(f"get_roles-URL:-{str(url)}")

            group_role = self.api_client.get(url).json()["result"]
            db_role = self.auth_repository.get_roles(searching, pagination)
            total_role = self.auth_repository.get_roles(searching)
            roles = self._map_role_data(group_role, db_role)
            return roles, len(total_role)  # type: ignore
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound()
                case _:
                    raise e

    def _map_role_data(
        self, group_role: list, db_role: list[model.DBRoleResponse]
    ) -> list[model.GroupRole]:
        # Create a dictionary from group_role with 'name' as the key
        group_role_dict = {role["name"]: role for role in group_role}
        # Map and convert roles in a single pass
        roles = [
            model.GroupRole(
                id=UUID(group_role_dict[role.role]["id"])
                if role.role in group_role_dict
                else role.role_uuid,
                name=role.role,
                isDefault=role.is_default,
                description=group_role_dict[role.role].get("description") or "N/A"
                if role.role in group_role_dict
                else "N/A",
                group=role.rolegroup,
                permissions=0,
                userCount=group_role_dict[role.role]["userCount"]
                if role.role in group_role_dict
                else 0,
                createdBy=role.created_by,
            )
            for role in db_role
        ]
        return roles

    def get_role_name_by_id(self, role_id: UUID) -> model.RoleResponse:
        try:
            logger.info("In get_role_name_by_id.....")

            url = self.build_url(self.GET_ROLE_BY_ID.format(role_id=role_id))
            logger.info(f"get_role_name_by_id-URL:-{str(url)}")
            response = self.api_client.get(url).json()
            if not response:
                logger.error(
                    "Error in get_role_name_by_id: Role not found "
                    f"associated with id {str(role_id)}"
                )
                raise RoleNotFound(role_id=role_id)
            return model.RoleResponse(**response)
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound()
                case _:
                    raise e

    def get_policies(self) -> dict:
        try:
            logger.info("In get_policies.....")

            url = self.build_url(self.GET_POLICIES)
            logger.info(f"get_policies-URL:-{str(url)}")
            response = self.api_client.get(url).json()
            return response
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound()
                case _:
                    raise e

    def get_policy_id_by_role_id(
        self, role_id: UUID
    ) -> tuple[dict, model.RoleResponse]:
        logger.info("In get_policy_id_by_role_id.....")

        role_data = self.get_role_name_by_id(role_id)
        policies = self.get_policies()
        policy = next(
            (
                {**policy}
                for policy in policies["result"]
                if policy["name"] == role_data.name
            ),
            None,
        )
        if not policy:
            logger.error(
                f"Error in function get_policy_id_by_role_id. Error: {str(role_id)}"
            )
            raise PolicyNotFound(role_id)
        return policy, role_data

    def get_permissions(
        self, role_id: UUID, organization_id: int | None = None
    ) -> model.PermissionsResponse:
        try:
            logger.info("In get_permissions.....")
            policy, role_data = self.get_policy_id_by_role_id(role_id)
            role_name = role_data.name
            role_info = self.auth_repository.get_role_info(role_name)
            url = self.build_url(
                self.GET_PERMISSIONS_BY_POLICY.format(policy_id=policy["id"])
            )
            logger.info(f"get_permissions-URL:-{str(url)}")
            response = self.api_client.get(url).json()
            if organization_id:
                name_mapping = {"ClientAdmin": "Admin", "ClientUser": "User"}
                for old_name, new_name in name_mapping.items():
                    role_name = role_name.replace(old_name, new_name)
            return model.PermissionsResponse(
                id=role_id,
                name=role_name,
                roleGroup=role_info.rolegroup.value,
                description=role_data.description or "N/A",
                permission=[
                    permission["id"] for permission in response["result"] or []
                ],
            )
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound()
                case _:
                    raise e

    def validate_role(
        self, role_request: model.RoleRequest
    ) -> tuple[bool, model.RoleResponse | None]:
        try:
            kc_role = None
            get_role_by_name_url = self.build_url(
                self.GET_ROLES, search=role_request.name
            )
            role_response = self.api_client.get(url=get_role_by_name_url)
            kc_roles = [
                model.RoleResponse(**role) for role in role_response.json()["result"]
            ]
            kc_role_data = [(role.name, role) for role in kc_roles]
            for role_name, role in kc_role_data:
                if role_name == role_request.name:
                    kc_role = role
                    break
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    kc_role = None
                case status.HTTP_409_CONFLICT:
                    raise PolicyAlreadyExist()
                case _:
                    raise e
        try:
            db_roles = self.auth_repository.get_role_info(role_request.name)
            if role_request.name == db_roles.role:
                db_role_exist = True
        except ValueError:
            db_role_exist = False
        if kc_role and db_role_exist:
            raise PolicyAlreadyExist
        return db_role_exist, kc_role

    def create_role(
        self,
        role_request: model.RoleRequest,
        organization_id: int | None = None,
        created_by: EmailStr | None = None,
    ) -> model.RoleResponse:
        try:
            url = self.build_url(self.GET_ROLES)
            db_role_exist, kc_role = self.validate_role(role_request)

            if not kc_role:
                json_data = role_request.dict()
                logger.info(f"Creating role in keycloak:url {url}, request {json_data}")
                role_data = self.api_client.post(
                    url=url,
                    headers=self._make_header(),
                    json={"attributes": {}, **json_data},
                )
                kc_role = model.RoleResponse(**role_data.json())
                self.auth_repository.update_role_uuid(kc_role)
                logger.info(f"Keycloak create role response {kc_role}")
            if not created_by:
                raise NotFound("No user found.")
            if kc_role and not db_role_exist:
                if not organization_id:
                    logger.info("creating role for DistributorAdmin")
                    if (
                        role_request.roleGroup.name
                        == model.RoleGroup.MY_ORGANIZATION.name
                    ):
                        self.auth_repository.create_role(
                            model.UserRole(
                                role=role_request.name,
                                rolegroup=role_request.roleGroup.name,
                                created_by=created_by,
                                role_uuid=kc_role.id,
                            )
                        )
                    elif role_request.roleGroup.name == model.RoleGroup.ACCOUNT.name:
                        self.auth_repository.create_role_for_accounts(
                            model.UserRole(
                                role=role_request.name,
                                rolegroup=role_request.roleGroup.name,
                                created_by=created_by,
                                role_uuid=kc_role.id,
                            ),
                        )
                elif organization_id:
                    logger.info("creating role for ClientAdmin")
                    self.auth_repository.create_role_for_client(
                        model.UserRole(
                            role=role_request.name,
                            rolegroup=role_request.roleGroup.name,
                            created_by=created_by,
                            role_uuid=kc_role.id,
                        ),
                        organization_id,
                    )
            return kc_role
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound()
                case status.HTTP_409_CONFLICT:
                    raise PolicyAlreadyExist()
                case _:
                    raise e

    def delete_role(self, role_uuid: UUID) -> None:
        try:
            url = self.build_url(self.GET_ROLE_BY_ID.format(role_id=role_uuid))
            if self.auth_repository.is_default(role_uuid):
                raise RoleDeletionError("Default role could not be deleted")
            self.auth_repository.delete_role(role_uuid)
            response = self.api_client.delete(url)
            logger.info(f"Role {role_uuid} deleted. Response - {response}")
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound()
                case status.HTTP_400_BAD_REQUEST:
                    raise RoleDeletionError()
                case _:
                    raise e

    def is_authorized(self, scope: str):
        try:
            url = self.build_url(self.GET_AUTHORIZED_SCOPE)
            response = self.api_client.post(
                url=url, headers=self._make_header(), json={"scope": scope}
            )
            logger.info(f'Response of {url} - {response.json()["status"]}')
            return response.json()["status"]
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError("Forbidden")
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound("NotFound")
                case status.HTTP_400_BAD_REQUEST:
                    logger.error(f"Authorization Error: {e.status_code}: {e.message}")
                    raise AuthException(f"{e.message}")
                case _:
                    raise e
        except Exception as e:
            logger.error(f"Error in function is_authorized. Error: {str(e)}")

    def get_user_scope(self) -> model.UserScopeList:
        try:
            logger.debug("api/token get_user_scope")
            url = self.build_url(self.GET_USER_SCOPE)
            response = self.api_client.get(url=url)
            logger.info(f"Response of {url} - {response}")
            return model.UserScopeList(**response.json())
        except PlatformAPIError as e:
            logger.error(f"Error in function get_user_scope. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise NotFound()
                case _:
                    raise e

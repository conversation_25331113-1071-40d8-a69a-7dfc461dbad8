from functools import cache
from typing import Callable

from fastapi import Depends
from fastapi.security import OAuth2, OAuth2AuthorizationCodeBearer
from platform_api_client import PlatformAPIClient

from app.config import logger, settings
from auth.services import TokenIntrospectionAuthService


@cache
def get_oauth_scheme() -> OAuth2:
    """
    OAuth2 compliant security scheme.
    Resolves a bearer token from request and provides a security context
    to OpenAPI schema.
    """

    if not settings.OIDC_TOKEN_URL:
        raise RuntimeError("OIDC_TOKEN_URL setting missing")
    if not settings.OIDC_AUTHORIZATION_URL:
        raise RuntimeError("OIDC_AUTHORIZATION_URL setting missing")

    oauth_scheme = OAuth2AuthorizationCodeBearer(
        authorizationUrl=settings.OIDC_AUTHORIZATION_URL,
        tokenUrl=settings.OIDC_TOKEN_URL,
        auto_error=True,
    )
    return oauth_scheme


@cache
def make_auth_service_factory() -> Callable[[], TokenIntrospectionAuthService]:
    """Create dependency returning TokenIntrospectionAuthService."""

    oauth_scheme = get_oauth_scheme()

    # assign to variables to bypass mypy checks in closure

    if not settings.OIDC_CLIENT_ID:
        raise RuntimeError("OIDC_CLIENT_ID setting missing")
    else:
        client_id = settings.OIDC_CLIENT_ID

    if not settings.OIDC_CLIENT_SECRET:
        raise RuntimeError("OIDC_CLIENT_SECRET setting missing")
    else:
        client_secret = settings.OIDC_CLIENT_SECRET

    if not settings.OIDC_TOKEN_INTROSPECTION_URL:
        raise RuntimeError("OIDC_TOKEN_INTROSPECTION_URL setting missing")
    else:
        introspection_url = settings.OIDC_TOKEN_INTROSPECTION_URL

    def _auth_service(
        token: str = Depends(oauth_scheme),
    ) -> TokenIntrospectionAuthService:
        return TokenIntrospectionAuthService(
            token,
            client_id=client_id,
            client_secret=client_secret,
            introspection_url=introspection_url,
        )

    return _auth_service


def get_service_access_token() -> str:
    """Return the access token for the current service using its client credentials."""

    data = dict(
        grant_type="client_credentials",
        client_id=settings.OIDC_CLIENT_ID,
        client_secret=settings.OIDC_CLIENT_SECRET,
    )
    with PlatformAPIClient() as client:
        response = client.post(str(settings.OIDC_TOKEN_URL), data=data)
        payload = response.json()
        return payload["access_token"]


def get_kafka_service_access_token() -> str:
    """Return the access token for the current service using its client credentials."""

    data = dict(
        grant_type="password",
        client_id=settings.KAFKA_OIDC_CLIENT_ID,
        client_secret=settings.KAFKA_OIDC_CLIENT_SECRET,
        username=settings.KAFKA_USERNAME,
        password=settings.KAFKA_PASSWORD,
    )
    with PlatformAPIClient() as client:
        logger.debug("getting token from Kafka server")
        response = client.post(str(settings.KAFKA_OIDC_TOKEN_URL), data=data)
        payload = response.json()
        return payload["access_token"]

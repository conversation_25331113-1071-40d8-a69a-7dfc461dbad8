from datetime import datetime
from uuid import uuid4

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    Date,
    DateTime,
    Enum,
    ForeignKey,
    Identity,
    Index,
    Integer,
    String,
    Table,
    UniqueConstraint,
    event,
    select,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import column_property, relationship

from common.db import mapper_registry
from common.types import IMSI, FormFactor
from sim.domain import model
from sim.domain.model import ICCID, MSISDN, PIN, PUK

sim_range = Table(
    "range",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("title", String(length=64), nullable=False),
    Column(
        "form_factor",
        Enum(
            FormFactor,
            create_constraint=True,
            native_enum=False,
            name="form_factor",
        ),
    ),
    Column("quantity", Integer, default=0),
    Column("imsi_first", String(length=IMSI.max_length), nullable=True),
    Column("imsi_last", String(length=IMSI.max_length), nullable=True),
    Column("remaining", Integer, default=0),
    Column("created_at", DateTime, default=datetime.utcnow),
    Column("created_by", String(length=128), nullable=False),
)

sim_card = Table(
    "sim_card",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("range_id", Integer, ForeignKey("range.id"), nullable=False),
    Column("allocation_id", Integer, ForeignKey("allocation.id"), nullable=True),
    Column(
        "rate_plan_id",
        Integer,
        ForeignKey("rate_plan.id", ondelete="RESTRICT"),
        nullable=True,
    ),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("imsi", String(length=IMSI.max_length), nullable=False),
    Column("msisdn", String(length=MSISDN.max_length), nullable=False),
    Column("pin_1", String(length=PIN.max_length), nullable=True),
    Column("pin_2", String(length=PIN.max_length), nullable=True),
    Column("puk_1", String(length=PUK.max_length), nullable=True),
    Column("puk_2", String(length=PUK.max_length), nullable=True),
    Column(
        "sim_status",
        Enum(model.SimStatus, name="sim_status"),
        nullable=True,
        default=model.SimStatus.READY_FOR_ACTIVATION,
    ),
    Index("idx_simcard_msisdn", "msisdn"),
    Index("idx_simcard_imsi", "imsi"),
)


sim_allocation = Table(
    "allocation",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("title", String(length=64), nullable=False),
    Column(
        "account_id",
        Integer,
        ForeignKey("account.id", ondelete="RESTRICT"),
        nullable=False,
    ),
    Column("range_id", Integer, ForeignKey("range.id")),
    Column(
        "rate_plan_id",
        Integer,
        ForeignKey("rate_plan.id", ondelete="RESTRICT"),
        nullable=True,
    ),
    Column("imsi", String(length=IMSI.max_length), nullable=True),
    Column("created_at", DateTime, default=datetime.utcnow),
    Column(
        "allocation_details_id",
        Integer,
        ForeignKey("allocation_details.id"),
        nullable=True,
    ),
    Column("created_by", String(length=128), nullable=True),
    Index("idx_allocation_imsi", "imsi"),
)

sim_allocation_details = Table(
    "allocation_details",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("file_name", String(), nullable=True),
    Column("total_sim", Integer, nullable=False),
    Column("error_sim", Integer, nullable=False),
)


sim_activity_log = Table(
    "sim_activity_log",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("uuid", postgresql.UUID(as_uuid=True), default=uuid4, nullable=False),
    Column("imsi", String(length=IMSI.max_length), nullable=False),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("msisdn", String(length=MSISDN.max_length), nullable=False),
    Column("request_type", String(), nullable=False),
    Column("prior_value", Enum(model.SimStatus, name="sim_status"), nullable=False),
    Column("new_value", Enum(model.SimStatus, name="sim_status"), nullable=False),
    Column("client_ip", String(), nullable=True),
    Column("created_at", DateTime, default=datetime.utcnow),
    Column("created_by", String(length=128), nullable=False),
    UniqueConstraint("uuid"),
)

sim_provider_log = Table(
    "sim_provider_log",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "sim_activity_log_uuid",
        postgresql.UUID(),
        # ForeignKey("sim_activity_log.uuid"),
        nullable=True,
    ),
    Column("audit_date", DateTime(), nullable=False),
    Column("message", String(), nullable=False),
    Column("status", String(), nullable=False),
    Column("work_id", String(), nullable=False),
    Column("prior_status", Enum(model.SimStatus, name="sim_status"), nullable=True),
)

active_sim_monthly_statistic = Table(
    "sim_monthly_statistic",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "sim_card_id",
        Integer,
        ForeignKey("sim_card.id"),
        nullable=False,
    ),
    Column("month", Date, nullable=False),
    Column("sim_status", Enum(model.SimStatus, name="sim_status"), nullable=False),
    Column("is_first_activation", Boolean, nullable=False),
)

sim_card_msisdn_update = Table(
    "sim_card_voice_sms_msisdn",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("imsi", String(length=IMSI.max_length), nullable=False),
    Column("msisdn", String(length=MSISDN.max_length), nullable=False),
)

msisdn_pool = Table(
    "msisdn_pool",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("msisdn", String(length=MSISDN.max_length), nullable=False),
    Column("sim_profile", Enum(model.SimProfile, name="sim_profile"), nullable=False),
    Column(
        "msisdn_factor", Enum(model.MSISDNFactor, name="msisdn_factor"), nullable=False
    ),
    Column("created_at", DateTime, default=datetime.utcnow, nullable=False),
    Column("uploaded_by", String(length=120), nullable=False),
    Column(
        "allocation_id",
        Integer,
        ForeignKey("allocation.id"),
        nullable=True,
    ),
    Index("ix_msisdn_pool_msisdn", "msisdn"),
    Index("ix_msisdn_pool_sim_profile", "sim_profile"),
    Index("ix_msisdn_pool_msisdn_factor", "msisdn_factor"),
)


@event.listens_for(model.Allocation, "after_delete")
def receive_after_delete(mapper, connection, target):
    """Listen for update Range remaining
    and remove SIMCard rate_plan_id
    after Allocation delete"""

    sim_cards_ids = map(lambda sim_card: sim_card.id, target.sim_cards)
    update_sim_cards_query = (
        sim_card.update()
        .values(rate_plan_id=None)
        .where(sim_card.c.id.in_(sim_cards_ids))
    )
    connection.execute(update_sim_cards_query)

    range_ = target._range
    range_.remaining += target.quantity
    update_sim_range_query = (
        sim_range.update()
        .values(remaining=range_.remaining)
        .where(sim_range.c.id == range_.id)
    )
    connection.execute(update_sim_range_query)


def start_mappers() -> None:
    mapper_registry.map_imperatively(
        model.Range,
        sim_range,
        properties=dict(
            _allocations=relationship(
                model.Allocation, back_populates="_range", cascade="all, delete-orphan"
            ),
            _sim_cards=relationship(
                model.SIMCard, back_populates="_range", cascade="all, delete-orphan"
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.SIMCard,
        sim_card,
        properties=dict(
            _range=relationship(model.Range, back_populates="_sim_cards"),
            _allocation=relationship(model.Allocation, back_populates="_sim_cards"),
            form_factor=column_property(
                select(sim_range.c.form_factor)
                .where(sim_card.c.range_id == sim_range.c.id)
                .scalar_subquery()
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.Allocation,
        sim_allocation,
        properties=dict(
            _range=relationship(model.Range, back_populates="_allocations"),
            _sim_cards=relationship(model.SIMCard, back_populates="_allocation"),
            _msisdn_pool=relationship(model.MsisdnPool, back_populates="_allocations"),
        ),
    )

    mapper_registry.map_imperatively(
        model.SIMActivityLog,
        sim_activity_log,
        # properties=dict(
        #     _provider_log=relationship(
        #         model.SIMProviderLog,
        #         back_populates="_activity",
        #         cascade="all, delete-orphan",
        #     ),
        # ),
    )
    mapper_registry.map_imperatively(
        model.SIMProviderLog,
        sim_provider_log,
        # properties=dict(
        #     _activity=relationship(
        #         model.SIMActivityLog, back_populates="_provider_log"
        #     ),
        # ),
    )
    mapper_registry.map_imperatively(
        model.SIMMonthlyStatus,
        active_sim_monthly_statistic,
    )
    mapper_registry.map_imperatively(
        model.sim_card_msisdn,
        sim_card_msisdn_update,
    )

    mapper_registry.map_imperatively(
        model.AllocationDetails,
        sim_allocation_details,
    )

    mapper_registry.map_imperatively(
        model.MsisdnPool,
        msisdn_pool,
        properties=dict(
            _allocations=relationship(
                model.Allocation,
                back_populates="_msisdn_pool",
            ),
        ),
    )


def stop_mappers() -> None:
    mapper_registry.dispose()

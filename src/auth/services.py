import logging
import os
import secrets
import uuid
from abc import ABC, abstractmethod
from functools import cached_property
from typing import Any, Callable

import authn
from pydantic import AnyHttpUrl, ValidationError, parse_obj_as

from auth.dto import (
    Account,
    Actor,
    Anonymous,
    AuthenticatedActor,
    AuthenticatedUser,
    Client,
    Distributor,
    Service,
    User,
)
from auth.exceptions import Unauthorized


class AbstractAuthService(ABC):
    @property
    @abstractmethod
    def current_user(self) -> Actor:
        ...

    @property
    def authenticated_user(self) -> AuthenticatedUser:
        if not isinstance(self.current_user, AuthenticatedUser):
            raise Unauthorized()
        return self.current_user

    @property
    def authenticated_service(self) -> Service:
        if not isinstance(self.current_user, Service):
            raise Unauthorized()
        return self.current_user

    @property
    def authenticated_actor(self) -> AuthenticatedActor:
        if not isinstance(self.current_user, (AuthenticatedUser, Service)):
            raise Unauthorized()
        return self.current_user


class TokenIntrospectionAuthService(AbstractAuthService):
    def __init__(
        self,
        access_token: str,
        client_id: str,
        client_secret: str,
        introspection_url: AnyHttpUrl,
    ):
        self.client_id = client_id
        self.client_secret = client_secret
        self.introspection_url = introspection_url
        self.token = access_token

    @cached_property
    def current_user(self) -> Actor:
        return self.introspect_token()

    def introspect_token(self) -> Actor:
        try:
            payload = authn.introspect_token(
                token=self.token,
                url=str(self.introspection_url),
                client_id=self.client_id,
                client_secret=self.client_secret,
            )
        except authn.AuthNError as e:
            logging.warning(f"Token introspection error: {e}")
            return Anonymous()

        return self.parse_payload(payload)

    @classmethod
    def parse_payload(cls, payload: dict[str, Any]) -> User | Service | Anonymous:
        token_type = payload.get("typ")
        TOKEN_TYPE = os.getenv("TOKEN_TYPE", "Bearer")
        if token_type != TOKEN_TYPE:
            logging.warning(f"Bad token type: {token_type}.")
            return Anonymous()

        try:
            return parse_obj_as(User, payload)  # type: ignore[arg-type]
        except ValidationError as e:
            if client_id := payload.get("clientId"):
                return Service(client_id=client_id)
            logging.warning(f"Bad introspection payload: {e}")
            return Anonymous()


class FakeAuthService(AbstractAuthService):
    def __init__(self, user: User | None = None):
        self.user = user or AuthenticatedUser(
            id=uuid.uuid4(),
            organization=Distributor(id=secrets.randbelow(10) + 1),
            role="User",
            email="<EMAIL>",
        )

    @property
    def current_user(self) -> Actor:
        return self.user


class AccountAuthService(AbstractAuthService):
    def __init__(
        self,
        auth_service: AbstractAuthService,
        get_account: Callable[[int], Account | None],
    ):
        self.auth_service = auth_service
        self.get_account = get_account

    @property
    def current_user(self) -> Actor:
        return self.auth_service.current_user

    @property
    def authenticated_user(self) -> AuthenticatedUser:
        user = super().authenticated_user
        if isinstance(user.organization, Client):
            user.organization.account = self.get_account(user.organization.id)
        return user

from typing import Final

MINUTE: Final[int] = 60
BYTES: Final[int] = 1
KB: Final[int] = 1024
MB: Final[int] = 1024 * 1024
GB: Final[int] = 1024 * 1024 * 1024
TB: Final[int] = 1024 * 1024 * 1024 * 1024
PB: Final[int] = 1024 * 1024 * 1024 * 1024 * 1024

DMINUTE: Final[int] = 1
DBYTES: Final[int] = 0.0009765625  # type: ignore
DKB: Final[int] = 1
DMB: Final[int] = 1024
DGB: Final[int] = 1024 * 1024
DTB: Final[int] = 1024 * 1024 * 1024
DPB: Final[int] = 1024 * 1024 * 1024 * 1024

YEAR_DASH_MONTH_REGEX: Final[str] = r"^20\d{2}-(0[1-9]|1[0-2])$"

PAYG = "PAYG"
INDI = "INDI"
FIXED = "FIXED"
FLEXI = "FLEXI"

# Rule Type
USMO = "USMO"

# Rule Category
CTDDU = "CTDDU"
MPDU = "MPDU"

# Rule Definition
DUESL = "DUESL"
MBDUT = "MBDUT"

REQUEST_MESSAGE = "We have received your request."
MSISDN_FIELD = "MSISDN"

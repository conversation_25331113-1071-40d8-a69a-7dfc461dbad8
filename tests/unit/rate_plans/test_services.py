from decimal import Decimal
from unittest.mock import MagicMock

from api.rate_plans.examples import (
    CREATE_UPDATE_RATE_MODEL,
    CREATE_UPDATE_RATE_PLAN_MODEL,
    GET_RATE_PLAN_MODELS,
)
from common.types import Service
from rate_plans.adapters.rate_plan_repository import AbstractRatePlanRepository
from rate_plans.domain import model
from rate_plans.exceptions import (
    AccountRatePlanNotFound,
    AlreadyDefaultRatePlan,
    RatePlanDoesNotExist,
    RatePlanModelNotFound,
)
from rate_plans.services import MediaService, RatePlanService
from sim.adapters.repository import AbstractSimRepository

import pytest  # isort: skip


@pytest.fixture
def mock_rate_plan():
    return model.RatePlan(
        _id=202,
        account_id=1,
        name="FIXED 202501",
        access_fee=Decimal("12.03"),
        is_default=False,
        sim_limit=100,
        _rate_groups=[
            model.RateGroup(
                rates=[
                    model.Rate(
                        range_from=0,
                        range_to=10240,
                        value=Decimal("12.03"),
                        range_unit="Min",
                        price_unit="Min",
                        overage_fee=Decimal("5.26"),
                        overage_unit="Min",
                        isoverage=True,
                        overage_per=60,
                    )
                ],
                rate_model_id=2,
                services={Service.VOICE_MO},
            ),
            model.RateGroup(
                rates=[
                    model.Rate(
                        range_from=0,
                        range_to=10241,
                        value=Decimal("3.24"),
                        range_unit="Min",
                        price_unit="Min",
                        overage_fee=Decimal("6.27"),
                        overage_unit="Min",
                        isoverage=False,
                        overage_per=60,
                    )
                ],
                rate_model_id=3,
                services={Service.VOICE_MT},
            ),
            model.RateGroup(
                rates=[
                    model.Rate(
                        range_from=0,
                        range_to=10242,
                        value=Decimal("12.03"),
                        range_unit="SMS",
                        price_unit="SMS",
                        overage_fee=Decimal("7.28"),
                        overage_unit="SMS",
                        isoverage=True,
                        overage_per=100,
                    )
                ],
                rate_model_id=4,
                services={Service.SMS_MO},
            ),
            model.RateGroup(
                rates=[
                    model.Rate(
                        range_from=0,
                        range_to=500,
                        value=Decimal("12.03"),
                        range_unit="KB",
                        price_unit="KB",
                        overage_fee=Decimal("5.26"),
                        overage_unit="KB",
                        isoverage=False,
                        overage_per=5,
                    )
                ],
                rate_model_id=3,
                services={Service.DATA},
            ),
        ],
    )


@pytest.fixture
def mock_rate_plan_service(mock_rate_plan):
    mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
    mock_media_service = MagicMock(spec=MediaService)
    mock_sim_repository = MagicMock(spec=AbstractSimRepository)

    rate_models = list(
        map(lambda rate_model: model.RateModel(**rate_model), GET_RATE_PLAN_MODELS)
    )
    mock_rate_plan_repository.get_rate_plans_models.return_value = model.RatePlanModels(
        result=rate_models
    )
    mock_rate_plan_repository.get_rate_plans_model_by_id.return_value = rate_models[1]

    create_updateed_rate_model = model.RateModel(**CREATE_UPDATE_RATE_MODEL)
    mock_rate_plan_repository.update_rate_plans_model_by_id.return_value = (
        create_updateed_rate_model
    )
    mock_rate_plan_repository.create_rate_plan_model.return_value = (
        create_updateed_rate_model
    )
    mock_rate_plan_repository.delete_rate_plan_model.return_value = None

    mock_sim_repository.allocation_count_by_account.return_value = 5
    mock_sim_repository.get_allocation_count_by_rate_plan.return_value = 5

    mock_rate_plan_repository.unset_default_rate_plan.return_value = None
    mock_rate_plan_repository.set_default_rate_plan.return_value = None
    mock_rate_plan_repository.get.return_value = mock_rate_plan

    mock_media_service.get_file_url.return_value = "https://example.com/logo.png"

    rate_plan_service_mock = RatePlanService(
        rate_plan_repository=mock_rate_plan_repository,
        media_service=mock_media_service,
        sim_repository=mock_sim_repository,
    )
    return rate_plan_service_mock


@pytest.fixture
def mock_rate_plan_error_service():
    def auth_factory(error_type, message):
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_media_service = MagicMock(spec=MediaService)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)

        mock_rate_plan_repository.get_rate_plans_models.return_value = (
            model.RatePlanModels(result=[])
        )

        mock_rate_plan_repository.get_rate_plans_models.side_effect = error_type(
            message
        )

        mock_rate_plan_repository.get_rate_plans_model_by_id.side_effect = error_type(
            message
        )

        mock_rate_plan_repository.update_rate_plans_model_by_id.side_effect = (
            error_type(message)
        )
        mock_rate_plan_repository.create_rate_plan_model.side_effect = error_type(
            message
        )
        mock_rate_plan_repository.delete_rate_plan_model.side_effect = error_type(
            message
        )

        rate_plan_service_mock = RatePlanService(
            rate_plan_repository=mock_rate_plan_repository,
            media_service=mock_media_service,
            sim_repository=mock_sim_repository,
        )
        return rate_plan_service_mock

    return auth_factory


class TestRatePlanService:
    def test_get_rate_plans_models_success(self, mock_rate_plan_service):
        response = mock_rate_plan_service.get_rate_plans_models()
        rate_models = list(
            map(lambda rate_model: model.RateModel(**rate_model), GET_RATE_PLAN_MODELS)
        )
        response_schema = model.RatePlanModels(result=rate_models)
        assert response.result == response_schema.result

    def test_get_rate_plans_models_not_found(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Rateplan models not found.",
        )

        with pytest.raises(RatePlanModelNotFound):
            mock_error_service.get_rate_plans_models()

    def test_get_rate_plans_model_by_id_success(self, mock_rate_plan_service):
        response = mock_rate_plan_service.get_rate_plans_model_by_id(
            id=2,
        )
        rate_models = list(
            map(lambda rate_model: model.RateModel(**rate_model), GET_RATE_PLAN_MODELS)
        )
        assert response == rate_models[1]
        assert isinstance(response, model.RateModel)
        assert response.value == 2
        assert response.title == "Individual Plan"

    def test_get_rate_plans_model_by_id_not_found(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Requested rateplan model not found.",
        )

        with pytest.raises(RatePlanModelNotFound):
            mock_error_service.get_rate_plans_model_by_id(
                id=2,
            )

    def test_update_rate_plans_model_by_id_success(self, mock_rate_plan_service):
        rate_model = model.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)
        response = mock_rate_plan_service.update_rate_plans_model_by_id(
            id=6,
            model=rate_model,
        )
        assert isinstance(response, model.RateModel)
        assert response.value == 6
        assert response.title == "Individual Plan"

    def test_update_rate_plans_model_by_id_not_found(
        self, mock_rate_plan_error_service
    ):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Requested rateplan model not found.",
        )
        rate_model = model.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)

        with pytest.raises(RatePlanModelNotFound):
            mock_error_service.update_rate_plans_model_by_id(
                id=6,
                model=rate_model,
            )

    def test_create_rate_plan_model_success(self, mock_rate_plan_service):
        rate_model = model.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)
        response = mock_rate_plan_service.create_rate_plan_model(
            model=rate_model,
        )
        assert isinstance(response, model.RateModel)
        assert response.value == 6
        assert response.title == "Individual Plan"

    def test_create_rate_plan_model_not_found(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Requested rateplan model not found.",
        )
        rate_model = model.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)

        with pytest.raises(RatePlanModelNotFound):
            mock_error_service.create_rate_plan_model(
                model=rate_model,
            )

    def test_delete_rate_plan_model_success(self, mock_rate_plan_service):
        response = mock_rate_plan_service.delete_rate_plan_model(
            rate_plan_model_id=1,
        )
        expected_response = None
        assert response == expected_response

    def test_delete_rate_plan_model_not_found(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Requested rateplan model not found.",
        )

        with pytest.raises(RatePlanModelNotFound):
            mock_error_service.delete_rate_plan_model(
                rate_plan_model_id=1,
            )

    def test_delete_rate_plan_model_does_not_exist(self):
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_media_service = MagicMock(spec=MediaService)
        mock_sim_repository = MagicMock(spec=AbstractSimRepository)

        mock_rate_plan_repository.get_rate_plans_model_by_id.return_value = None

        rate_plan_service_mock = RatePlanService(
            rate_plan_repository=mock_rate_plan_repository,
            media_service=mock_media_service,
            sim_repository=mock_sim_repository,
        )

        with pytest.raises(RatePlanDoesNotExist):
            rate_plan_service_mock.delete_rate_plan_model(
                rate_plan_model_id=1,
            )

    def test_set_default_rate_plan_success(self, mock_rate_plan_service):

        mock_rate_plan_service.set_default_rate_plan(account_id=1, id=2)

        (
            mock_rate_plan_service.rate_plan_repository
        ).unset_default_rate_plan.assert_called_once_with(account_id=1)
        (
            mock_rate_plan_service.rate_plan_repository
        ).set_default_rate_plan.assert_called_once_with(id=2)

    def test_set_default_rate_plan_account_not_found(self, mock_rate_plan_service):

        with pytest.raises(AccountRatePlanNotFound):
            mock_rate_plan_service.set_default_rate_plan(account_id=20, id=2)

        (
            mock_rate_plan_service.rate_plan_repository
        ).unset_default_rate_plan.assert_not_called()
        (
            mock_rate_plan_service.rate_plan_repository
        ).set_default_rate_plan.assert_not_called()

    def test_set_default_rate_plan_already_default(
        self, mock_rate_plan_service, mock_rate_plan
    ):
        mock_rate_plan.is_default = True
        mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
        mock_rate_plan_repository.get.return_value = mock_rate_plan

        with pytest.raises(AlreadyDefaultRatePlan):
            mock_rate_plan_service.set_default_rate_plan(account_id=1, id=2)

        (
            mock_rate_plan_service.rate_plan_repository
        ).unset_default_rate_plan.assert_not_called()
        (
            mock_rate_plan_service.rate_plan_repository
        ).set_default_rate_plan.assert_not_called()

    def test_get_existing_rate_plan(self, mock_rate_plan_service, mock_rate_plan):

        result = mock_rate_plan_service.get(202)

        assert result == mock_rate_plan
        mock_rate_plan_service.rate_plan_repository.get.assert_called_once_with(202)

    def test_get_non_existing_rate_plan(self, mock_rate_plan_service):
        mock_rate_plan_service.rate_plan_repository.get.return_value = None

        with pytest.raises(RatePlanDoesNotExist):
            mock_rate_plan_service.get(999)

    def test_delete_rate_plan(self, mock_rate_plan_service, mock_rate_plan):

        mock_rate_plan_service.rate_plan_repository.get.return_value = mock_rate_plan

        mock_rate_plan_service.delete(207)

        mock_rate_plan_service.rate_plan_repository.delete.assert_called_once_with(
            mock_rate_plan
        )

import unittest
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON>ck, patch
from uuid import UUID, uuid4

import pytest
from platform_api_client import PlatformAPIError
from pydantic import BaseModel

from api.authorization.examples import GROUP_LIST, GROUP_ROLE_RESPONSE, PERMISSION_LIST
from app.config import settings
from auth.exceptions import ForbiddenError, NotFound
from authorization.domain import model
from authorization.domain.ports import AbstractAuthorizationAPI
from authorization.exceptions import (
    PolicyAlreadyExist,
    PolicyNotFound,
    RoleDeletionError,
    RoleNotFound,
)
from authorization.services import FakeAuthorizationAPI, HTTPAuthorizationAPI
from common.pagination import Pagination

ROLE_ID = "ac34204e-e9eb-495d-8b9b-bf69271cbd44"
GROUP_ID = "e8f01a81-c9d6-44d9-93f1-01cfdba44a46"


class TestAuthorization:
    @pytest.fixture
    def fake_authorization_service(self, *args, **kwargs) -> AbstractAuthorizationAPI:
        return FakeAuthorizationAPI()

    def test_get_group_role(self, fake_authorization_service):
        response, total_count = fake_authorization_service.get_group_role(1)
        assert total_count == 1
        assert response == GROUP_ROLE_RESPONSE

    def test_get_id_by_name(self, fake_authorization_service):
        group_list = fake_authorization_service.group_list()
        result = fake_authorization_service.get_id_by_name(
            "bt-push-cdr-account-nested", group_list
        )
        assert result == "c095050c-3f51-4d20-a266-bcb72f4d4f0d"

    def test_get_permissions(self, fake_authorization_service):
        result = fake_authorization_service.get_permissions(
            "c095050c-3f51-4d20-a266-bcb72f4d4f0d"
        )
        assert result == PERMISSION_LIST

    def test_get_roles(self, fake_authorization_service):
        response, total = fake_authorization_service.get_roles()
        assert list(response) == GROUP_ROLE_RESPONSE
        assert total == 1

    def test_group_list(self, fake_authorization_service):
        result = fake_authorization_service.group_list()
        assert result == GROUP_LIST

    def test_get_id_by_name_empty_list(self, fake_authorization_service):
        group_list = {"result": []}
        result = fake_authorization_service.get_id_by_name(
            "bt-push-cdr-account-nested", group_list
        )
        assert result is None

    def test_delete_role(self, fake_authorization_service):
        result = fake_authorization_service.delete_role(ROLE_ID)
        assert result is None

    def test_is_authorized(self, fake_authorization_service):
        result = fake_authorization_service.is_authorized("scope_name")
        assert result == "PERMIT"


class TestHTTPAuthorizationAPI(unittest.TestCase):
    def setUp(self):
        self.api_client = Mock()
        self.account_repository = Mock()
        self.auth_repository = Mock()
        self.http_authorization = HTTPAuthorizationAPI(
            api_client=self.api_client,
            account_repository=self.account_repository,
            auth_repository=self.auth_repository,
        )

    def test_build_url(self):
        url = self.http_authorization.build_url("/url")
        assert url == settings.APP_BASE_URL + "/url"

    def test_build_url_with_pagination_and_search(self):
        url = self.http_authorization.build_url(
            "/url", pagination=Pagination(page=1, page_size=10), search="search"
        )
        assert url == settings.APP_BASE_URL + "/url?page=1&page_size=10&search=search"

    def test_build_url_with_pagination_only(self):
        url = self.http_authorization.build_url(
            "/url", pagination=Pagination(page=1, page_size=10)
        )
        assert url == settings.APP_BASE_URL + "/url?page=1&page_size=10"

    def test_build_url_with_search_only(self):
        url = self.http_authorization.build_url("/url", search="search")
        assert url == settings.APP_BASE_URL + "/url?search=search"

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_group_list_successful(self, mocked_build_url):
        mock_group_data = {
            "results": [{"id": "1", "name": "Group1"}, {"id": "2", "name": "Group2"}]
        }
        self.api_client.get.return_value.json.return_value = mock_group_data
        group_info = self.http_authorization.group_list()
        self.assertEqual(group_info, mock_group_data)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_group_list_api_error(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=400, message="API Error"
        )
        with self.assertRaises(PlatformAPIError):
            self.http_authorization.group_list()

    @patch.object(
        HTTPAuthorizationAPI, "build_url", side_effect=Exception("General Error")
    )
    def test_group_list_general_error(self, mocked_build_url):
        with self.assertRaises(Exception):
            self.http_authorization.group_list()

    def test_get_id_by_name_successful(self):
        mock_group_data = {
            "results": [
                {"id": "1", "name": "Group1"},
                {
                    "id": "2",
                    "name": "Group2",
                    "subGroups": [{"id": "3", "name": "SubGroup1"}],
                },
            ]
        }
        group_name = "Group1"
        group_id = self.http_authorization.get_id_by_name(group_name, mock_group_data)
        self.assertEqual(group_id, "1")

    def test_get_id_by_name_nonexistent_group(self):
        mock_group_data = {
            "results": [{"id": "1", "name": "Group1"}, {"id": "2", "name": "Group2"}]
        }
        group_name = "NonexistentGroup"
        group_id = self.http_authorization.get_id_by_name(group_name, mock_group_data)
        self.assertIsNone(group_id)

    @patch.object(
        HTTPAuthorizationAPI,
        "_map_role_data",
        return_value=[
            model.GroupRole(
                id=UUID("6b326ce0-106e-489c-8bad-3428a34f1014"), name="Role1"
            ),
            model.GroupRole(
                id=UUID("6b326ce0-106e-489c-8bad-3428a34f1014"), name="Role2"
            ),
        ],
    )
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_group_role_successful(self, mocked_build_url, mock_map_role):
        mock_group_role_data = {
            "result": [
                {
                    "id": "6b326ce0-106e-489c-8bad-3428a34f1014",
                    "group": "Account",
                    "name": "Role1",
                    "description": "N/A",
                    "userCount": 0,
                },
                {
                    "id": "6b326ce0-106e-489c-8bad-3428a34f1014",
                    "group": "My Organization",
                    "name": "Role2",
                    "description": "N/A",
                    "userCount": 0,
                },
            ]
        }
        self.api_client.get.return_value.json.return_value = mock_group_role_data
        mock_db_role = [
            MagicMock(role="Role1", is_default=True, created_by="User1"),
            MagicMock(role="Role2", is_default=False, created_by="User2"),
        ]
        self.http_authorization.auth_repository.get_roles_by_organization_id.return_value = (  # noqa
            mock_db_role
        )
        group_roles, total_count = self.http_authorization.get_group_role(12)
        self.assertEqual(total_count, len(mock_group_role_data["result"]))
        self.assertEqual(len(list(group_roles)), len(mock_group_role_data["result"]))

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_group_role_successful_empty(self, mocked_build_url):
        mock_group_role_data = {
            "result": [
                {
                    "id": "6b326ce0-106e-489c-8bad-3428a34f1014",
                    "group": "Account",
                    "name": "Role1",
                    "description": "N/A",
                },
                {
                    "id": "6b326ce0-106e-489c-8bad-3428a34f1014",
                    "group": "My Organization",
                    "name": "Role2",
                    "description": "N/A",
                },
            ]
        }
        self.api_client.get.return_value.json.return_value = mock_group_role_data
        mock_db_role = []
        self.http_authorization.auth_repository.get_roles_by_organization_id.return_value = (  # noqa
            mock_db_role
        )
        group_roles, total_count = self.http_authorization.get_group_role(12)
        self.assertEqual(total_count, 0)
        self.assertEqual(len(list(group_roles)), 0)

    @patch.object(
        HTTPAuthorizationAPI,
        "_map_role_data",
        return_value=[
            model.GroupRole(
                id=UUID("6b326ce0-106e-489c-8bad-3428a34f1014"), name="Role1"
            ),
            model.GroupRole(
                id=UUID("6b326ce0-106e-489c-8bad-3428a34f1014"), name="Role2"
            ),
        ],
    )
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_roles_successful(self, mocked_build_url, mock_map_role_data):
        mock_roles_data = {
            "result": [
                {
                    "id": "8b376afc-9014-49ff-ad1f-4589e122deda",
                    "name": "Role1",
                    "group": "Account",
                    "description": "N/A",
                    "userCount": 0,
                },
                {
                    "id": "2a32fafc-aa14-49ff-111f-a119e122daaa",
                    "name": "Role2",
                    "group": "My Organization",
                    "description": "N/A",
                    "userCount": 0,
                },
            ]
        }

        self.api_client.get.return_value.json.return_value = mock_roles_data
        mock_db_role = [
            MagicMock(role="Role1", is_default=True, created_by="User1"),
            MagicMock(role="Role2", is_default=False, created_by="User2"),
        ]
        self.http_authorization.auth_repository.get_roles.return_value = mock_db_role
        roles, total_count = self.http_authorization.get_roles()
        self.assertEqual(total_count, len(mock_roles_data["result"]))
        self.assertIsInstance(roles, list)
        self.assertEqual(len(list(roles)), len(mock_roles_data["result"]))

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_roles_empty_roles(self, mocked_build_url):
        mock_roles_data = {"result": []}
        self.api_client.get.return_value.json.return_value = mock_roles_data
        mock_db_role = []
        self.http_authorization.auth_repository.get_roles.return_value = mock_db_role
        roles, total_count = self.http_authorization.get_roles()
        self.assertEqual(total_count, 0)
        self.assertIsInstance(roles, list)
        self.assertEqual(len(list(roles)), 0)

    @patch.object(
        HTTPAuthorizationAPI,
        "_map_role_data",
        return_value=[
            model.GroupRole(
                id=UUID("6b326ce0-106e-489c-8bad-3428a34f1014"), name="Role1"
            ),
            model.GroupRole(
                id=UUID("6b326ce0-106e-489c-8bad-3428a34f1014"), name="Role2"
            ),
        ],
    )
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_roles_pagination(self, mocked_build_url, mock_map_role_data):
        mock_roles_data = {
            "result": [
                {
                    "id": "8b376afc-9014-49ff-ad1f-4589e122deda",
                    "name": "Role1",
                    "group": "Account",
                    "description": "N/A",
                    "userCount": 0,
                },
                {
                    "id": "8b376afc-9014-49ff-ad1f-4589e122deda",
                    "name": "Role2",
                    "group": "My Organization",
                    "description": "N/A",
                    "userCount": 0,
                },
            ]
        }
        pagination = Pagination(page=1, page_size=11)
        self.api_client.get.return_value.json.return_value = mock_roles_data
        mock_db_role = [
            MagicMock(role="Role1", is_default=True, created_by="User1"),
            MagicMock(role="Role2", is_default=False, created_by="User2"),
        ]
        self.http_authorization.auth_repository.get_roles.return_value = mock_db_role
        roles, total_count = self.http_authorization.get_roles(pagination=pagination)
        self.assertEqual(total_count, len(mock_roles_data["result"]))
        self.assertIsInstance(roles, list)
        self.assertEqual(len(list(roles)), 2)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_roles_api_error(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=400, message="API Error"
        )
        with self.assertRaises(PlatformAPIError):
            self.http_authorization.get_roles()

    @patch.object(
        HTTPAuthorizationAPI, "build_url", side_effect=Exception("General Error")
    )
    def test_get_roles_general_error(self, mocked_build_url):
        with self.assertRaises(Exception):
            self.http_authorization.get_roles()

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_roles_not_found_404(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=404, message="API Error"
        )
        with self.assertRaises(NotFound):
            self.http_authorization.get_roles()

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_roles_forbidden_403(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=403, message="API Error"
        )
        with self.assertRaises(ForbiddenError):
            self.http_authorization.get_roles()

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_group_role_not_found_404(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=404, message="API Error"
        )
        with self.assertRaises(NotFound):
            self.http_authorization.get_group_role(1)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_group_role_forbidden_403(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=403, message="API Error"
        )
        with self.assertRaises(ForbiddenError):
            self.http_authorization.get_group_role(1)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_group_role_general_error(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=500, message="API Error"
        )
        with self.assertRaises(Exception):
            self.http_authorization.get_group_role(1)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_role_name_by_id_successful(self, mocked_build_url):
        mock_role_data = {
            "id": ROLE_ID,
            "name": "Role1",
            "description": "desc",
            "composite": False,
            "clientRole": True,
            "containerId": "mocked_container_id",
        }
        self.api_client.get.return_value.json.return_value = mock_role_data
        role_data = self.http_authorization.get_role_name_by_id(ROLE_ID)
        self.assertEqual(role_data.name, mock_role_data["name"])

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_role_name_by_id_role_not_found(self, mocked_build_url):
        self.api_client.get.return_value.json.return_value = None
        with pytest.raises(RoleNotFound) as e:
            self.http_authorization.get_role_name_by_id(ROLE_ID)
        assert f"Role not found associated with id {ROLE_ID}" in str(e)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_role_name_by_id_api_error(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=400, message="API Error"
        )
        with self.assertRaises(PlatformAPIError):
            self.http_authorization.get_role_name_by_id(ROLE_ID)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_role_name_by_id_api_error_403(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=403, message="API Error"
        )
        with self.assertRaises(ForbiddenError):
            self.http_authorization.get_role_name_by_id(ROLE_ID)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_role_name_by_id_api_error_404(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=404, message="API Error"
        )
        with self.assertRaises(NotFound):
            self.http_authorization.get_role_name_by_id(ROLE_ID)

    @patch.object(
        HTTPAuthorizationAPI, "build_url", side_effect=Exception("General Error")
    )
    def test_get_role_name_by_id_general_error(self, mocked_build_url):
        with self.assertRaises(Exception):
            self.http_authorization.get_role_name_by_id(ROLE_ID)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_policies_successful(self, mocked_build_url):
        mock_policies_data = {
            "policy": [
                {"id": "policy1", "name": "Policy1"},
                {"id": "policy2", "name": "Policy2"},
            ]
        }
        self.api_client.get.return_value.json.return_value = mock_policies_data
        policies = self.http_authorization.get_policies()
        self.assertEqual(policies, mock_policies_data)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_policies_api_error(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=400, message="API Error"
        )
        with self.assertRaises(PlatformAPIError):
            self.http_authorization.get_policies()

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_policies_api_error_404(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=404, message="API Error"
        )
        with self.assertRaises(NotFound):
            self.http_authorization.get_policies()

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_policies_api_error_403(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=403, message="API Error"
        )
        with self.assertRaises(ForbiddenError):
            self.http_authorization.get_policies()

    @patch.object(
        HTTPAuthorizationAPI, "build_url", side_effect=Exception("General Error")
    )
    def test_get_policies_general_error(self, mocked_build_url):
        with self.assertRaises(Exception):
            self.http_authorization.get_policies()

    @patch.object(
        HTTPAuthorizationAPI,
        "get_role_name_by_id",
        return_value=model.RoleResponse(
            id=ROLE_ID,
            name="Role1",
            description="short description",
            composite=False,
            clientRole=True,
            containerId="mocked_container_id",
        ),
    )
    @patch.object(
        HTTPAuthorizationAPI,
        "get_policies",
        return_value={
            "result": [
                {
                    "id": "uuid",
                    "name": "Role1",
                    "description": "desc",
                    "composite": False,
                    "clientRole": True,
                    "containerId": "mocked_container_id",
                },
                {
                    "id": "uuid",
                    "name": "Role2",
                    "description": "desc",
                    "composite": False,
                    "clientRole": True,
                    "containerId": "mocked_container_id",
                },
            ]
        },
    )
    def test_get_policy_id_by_role_id_successful(
        self, mocked_get_policies, mocked_get_role_name_by_id
    ):
        policy, role_response = self.http_authorization.get_policy_id_by_role_id(
            ROLE_ID
        )
        self.assertIsInstance(policy, dict)
        self.assertIsInstance(role_response, model.RoleResponse)

    @patch.object(
        HTTPAuthorizationAPI,
        "get_role_name_by_id",
        return_value=model.RoleResponse(
            id=ROLE_ID,
            name="ClientAdmin",
            description="short description",
            composite=False,
            clientRole=True,
            containerId="mocked_container_id",
        ),
    )
    def test_get_policy_id_by_role_id_role_not_found(self, mocked_get_role_name_by_id):
        mock_policies_data = {
            "result": [
                {"id": "policy1", "name": "Not Role 1"},
                {"id": "policy2", "name": "Not Role 2"},
            ]
        }
        with patch.object(
            self.http_authorization, "get_policies", return_value=mock_policies_data
        ):
            with self.assertRaises(PolicyNotFound):
                self.http_authorization.get_policy_id_by_role_id(ROLE_ID)

    @patch.object(
        HTTPAuthorizationAPI,
        "get_policy_id_by_role_id",
        return_value=(
            {
                "id": UUID("00000000-0000-0000-0000-000000000000"),
                "name": "ClientAdmin",
                "description": "short description",
                "permissions": [],
            },
            model.RoleResponse(
                id=ROLE_ID,
                name="ClientAdmin",
                description="short description",
                composite=False,
                clientRole=True,
                containerId="mocked_container_id",
            ),
        ),
    )
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_permissions_successful(
        self, mocked_build_url, mocked_get_role_name_by_id
    ):
        mock_permissions_data = {
            "result": [{"id": "permission uuid"}, {"id": "permission uuid"}]
        }
        self.http_authorization.auth_repository.get_role_info.return_value = (
            model.DBRoleResponse(
                role_uuid=uuid4(),
                role="ClientAdmin",
                rolegroup="My Organization",
                created_by="<EMAIL>",
                is_default=False,
            )
        )
        self.api_client.get.return_value.json.return_value = mock_permissions_data
        permissions_response = self.http_authorization.get_permissions(ROLE_ID)
        self.assertIsInstance(permissions_response, model.PermissionsResponse)
        self.assertEqual(
            permissions_response.permission, ["permission uuid", "permission uuid"]
        )

    @patch.object(
        HTTPAuthorizationAPI,
        "get_policy_id_by_role_id",
        return_value=(
            {
                "id": UUID("00000000-0000-0000-0000-000000000000"),
                "name": "ClientAdmin",
                "description": "short description",
                "permissions": [],
            },
            model.RoleResponse(
                id=ROLE_ID,
                name="ClientAdmin",
                description="short description",
                composite=False,
                clientRole=True,
                containerId="mocked_container_id",
            ),
        ),
    )
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_permissions_successful_as_client(
        self, mocked_build_url, mocked_get_role_name_by_id
    ):
        mock_permissions_data = {
            "result": [{"id": "permission uuid"}, {"id": "permission uuid"}]
        }
        self.http_authorization.auth_repository.get_role_info.return_value = (
            model.DBRoleResponse(
                role_uuid=uuid4(),
                role="ClientAdmin",
                rolegroup="My Organization",
                created_by="<EMAIL>",
                is_default=False,
            )
        )
        self.api_client.get.return_value.json.return_value = mock_permissions_data
        permissions_response = self.http_authorization.get_permissions(ROLE_ID, 1)
        assert permissions_response.name == "Admin"
        self.assertIsInstance(permissions_response, model.PermissionsResponse)
        self.assertEqual(
            permissions_response.permission, ["permission uuid", "permission uuid"]
        )

    @patch.object(
        HTTPAuthorizationAPI,
        "get_policy_id_by_role_id",
        return_value=(
            {
                "id": UUID("00000000-0000-0000-0000-000000000000"),
                "name": "ClientAdmin",
                "description": "short description",
                "permissions": [],
            },
            model.RoleResponse(
                id=ROLE_ID,
                name="ClientAdmin",
                description="short description",
                composite=False,
                clientRole=True,
                containerId="mocked_container_id",
            ),
        ),
    )
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_permissions_api_error(
        self, mocked_build_url, mocked_get_role_name_by_id
    ):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=400, message="API Error"
        )
        with self.assertRaises(PlatformAPIError):
            self.http_authorization.get_permissions(ROLE_ID)

    @patch.object(
        HTTPAuthorizationAPI,
        "get_policy_id_by_role_id",
        side_effect=PolicyNotFound(ROLE_ID),
    )
    def test_get_permissions_policy_not_found(self, mocked_get_role_name_by_id):
        with self.assertRaises(PolicyNotFound):
            self.http_authorization.get_permissions(ROLE_ID)

    @patch.object(
        HTTPAuthorizationAPI,
        "get_policy_id_by_role_id",
        return_value=(
            {
                "id": UUID("00000000-0000-0000-0000-000000000000"),
                "name": "ClientAdmin",
                "description": "short description",
                "permissions": [],
            },
            model.RoleResponse(
                id=ROLE_ID,
                name="ClientAdmin",
                description="short description",
                composite=False,
                clientRole=True,
                containerId="mocked_container_id",
            ),
        ),
    )
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_permissions_api_error_404(
        self, mocked_build_url, mocked_get_role_name_by_id
    ):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=404, message="API Error"
        )
        with self.assertRaises(NotFound):
            self.http_authorization.get_permissions(ROLE_ID)

    @patch.object(
        HTTPAuthorizationAPI,
        "get_policy_id_by_role_id",
        return_value=(
            {
                "id": UUID("00000000-0000-0000-0000-000000000000"),
                "name": "ClientAdmin",
                "description": "short description",
                "permissions": [],
            },
            model.RoleResponse(
                id=ROLE_ID,
                name="ClientAdmin",
                description="short description",
                composite=False,
                clientRole=True,
                containerId="mocked_container_id",
            ),
        ),
    )
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_permissions_forbidden_error_403(
        self, mocked_build_url, mocked_get_role_name_by_id
    ):
        self.api_client.get.side_effect = PlatformAPIError(
            status_code=403, message="API Error"
        )
        with self.assertRaises(ForbiddenError):
            self.http_authorization.get_permissions(ROLE_ID)

    @patch.object(
        HTTPAuthorizationAPI, "get_policy_id_by_role_id", return_value="policy uuid"
    )
    @patch.object(
        HTTPAuthorizationAPI, "build_url", side_effect=Exception("General Error")
    )
    def test_get_permissions_general_error(
        self, mocked_build_url, mocked_get_role_name_by_id
    ):
        with self.assertRaises(Exception):
            self.http_authorization.get_permissions(ROLE_ID)

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_delete_role_successful(self, mocked_build_url):
        self.api_client.delete.return_value.status_code = 204
        role_uuid = UUID("8b376afc-9014-49ff-ad1f-4589e122deda")
        self.http_authorization.auth_repository.is_default.return_value = False
        self.http_authorization.auth_repository.delete_role.return_value = None
        self.http_authorization.delete_role(role_uuid)

        mocked_build_url.assert_called_once_with(
            "/v1/authorization/role/8b376afc-9014-49ff-ad1f-4589e122deda"
        )
        self.api_client.delete.assert_called_once_with("mocked_url")
        self.http_authorization.auth_repository.delete_role.assert_called_once_with(
            role_uuid
        )

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_delete_role_default_role(self, mocked_build_url):
        role_uuid = UUID("8b376afc-9014-49ff-ad1f-4589e122deda")
        self.http_authorization.auth_repository.is_default.return_value = True

        with self.assertRaises(RoleDeletionError) as context:
            self.http_authorization.delete_role(role_uuid)

        self.assertEqual(str(context.exception), "Default role could not be deleted")

        mocked_build_url.assert_called_once_with(
            "/v1/authorization/role/8b376afc-9014-49ff-ad1f-4589e122deda"
        )
        self.api_client.delete.assert_not_called()
        self.http_authorization.auth_repository.delete_role.assert_not_called()

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_delete_role_api_error(self, mocked_build_url):
        role_uuid = UUID("8b376afc-9014-49ff-ad1f-4589e122deda")

        with self.assertRaises(RoleDeletionError) as context:
            self.http_authorization.delete_role(role_uuid)

        self.assertEqual(str(context.exception), "Default role could not be deleted")

        mocked_build_url.assert_called_once_with(
            "/v1/authorization/role/8b376afc-9014-49ff-ad1f-4589e122deda"
        )
        self.api_client.delete.assert_not_called()
        self.http_authorization.auth_repository.delete_role.assert_not_called()

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_delete_role_api_error_404(self, mocked_build_url):
        role_uuid = UUID("8b376afc-9014-49ff-ad1f-4589e122deda")
        self.http_authorization.auth_repository.is_default.return_value = False
        self.api_client.delete.side_effect = PlatformAPIError(
            status_code=404, message=""
        )

        with self.assertRaises(NotFound) as context:
            self.http_authorization.delete_role(role_uuid)

        self.assertEqual(str(context.exception), "")

        mocked_build_url.assert_called_once_with(
            "/v1/authorization/role/8b376afc-9014-49ff-ad1f-4589e122deda"
        )
        self.api_client.delete.assert_called_once_with("mocked_url")

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_delete_role_api_error_403(self, mocked_build_url):
        role_uuid = UUID("8b376afc-9014-49ff-ad1f-4589e122deda")
        self.http_authorization.auth_repository.is_default.return_value = False
        self.api_client.delete.side_effect = PlatformAPIError(
            status_code=403, message=""
        )

        with self.assertRaises(ForbiddenError) as context:
            self.http_authorization.delete_role(role_uuid)

        self.assertEqual(str(context.exception), "")

        mocked_build_url.assert_called_once_with(
            "/v1/authorization/role/8b376afc-9014-49ff-ad1f-4589e122deda"
        )
        self.api_client.delete.assert_called_once_with("mocked_url")

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_delete_role_api_error_unknown_error(self, mocked_build_url):
        role_uuid = UUID("8b376afc-9014-49ff-ad1f-4589e122deda")
        self.http_authorization.auth_repository.is_default.return_value = False
        self.api_client.delete.side_effect = PlatformAPIError(
            status_code=500, message=""
        )

        with self.assertRaises(PlatformAPIError) as context:
            self.http_authorization.delete_role(role_uuid)

        self.assertEqual(str(context.exception), "")

        mocked_build_url.assert_called_once_with(
            "/v1/authorization/role/8b376afc-9014-49ff-ad1f-4589e122deda"
        )
        self.api_client.delete.assert_called_once_with("mocked_url")

    @patch.object(HTTPAuthorizationAPI, "validate_role", return_value=(True, None))
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_create_role_distributor_admin_with_403(
        self, mocked_build_url, mock_validate_role
    ):
        role_request = model.RoleRequest(
            name="TestRole",
            description="Test Description",
            roleGroup=model.RoleGroup.MY_ORGANIZATION,
            permission=["8b376afc-9014-49ff-ad1f-4589e122deda"],
        )
        self.api_client.post.side_effect = PlatformAPIError(status_code=403, message="")

        with self.assertRaises(ForbiddenError) as context:
            self.http_authorization.create_role(role_request)

        self.assertEqual(str(context.exception), "")
        self.http_authorization.auth_repository.create_role.assert_not_called()

    @patch.object(HTTPAuthorizationAPI, "validate_role", return_value=(True, None))
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_create_role_distributor_admin_with_404(
        self, mocked_build_url, mock_validate_role
    ):
        role_request = model.RoleRequest(
            name="TestRole",
            description="Test Description",
            roleGroup=model.RoleGroup.MY_ORGANIZATION,
            permission=["8b376afc-9014-49ff-ad1f-4589e122deda"],
        )
        self.api_client.post.side_effect = PlatformAPIError(status_code=404, message="")

        with self.assertRaises(NotFound) as context:
            self.http_authorization.create_role(role_request)

        self.assertEqual(str(context.exception), "")
        self.http_authorization.auth_repository.create_role.assert_not_called()

    @patch.object(HTTPAuthorizationAPI, "validate_role", return_value=(True, None))
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_create_role_distributor_admin_with_409(
        self, mocked_build_url, mock_validate_role
    ):
        role_request = model.RoleRequest(
            name="TestRole",
            description="Test Description",
            roleGroup=model.RoleGroup.MY_ORGANIZATION,
            permission=["8b376afc-9014-49ff-ad1f-4589e122deda"],
        )
        self.api_client.post.side_effect = PlatformAPIError(status_code=409, message="")

        with self.assertRaises(PolicyAlreadyExist) as context:
            self.http_authorization.create_role(role_request)

        self.assertEqual(str(context.exception), "")
        self.http_authorization.auth_repository.create_role.assert_not_called()

    @patch.object(HTTPAuthorizationAPI, "validate_role", return_value=(True, None))
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_create_role_distributor_admin_with_unknown_exception(
        self, mocked_build_url, mock_validate_role
    ):
        role_request = model.RoleRequest(
            name="TestRole",
            description="Test Description",
            roleGroup=model.RoleGroup.MY_ORGANIZATION,
            permission=["8b376afc-9014-49ff-ad1f-4589e122deda"],
        )
        self.api_client.post.side_effect = PlatformAPIError(status_code=500, message="")

        with self.assertRaises(PlatformAPIError) as context:
            self.http_authorization.create_role(role_request)

        self.assertEqual(str(context.exception), "")
        self.http_authorization.auth_repository.create_role.assert_not_called()

    @patch.object(HTTPAuthorizationAPI, "validate_role", return_value=(True, None))
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    @patch.object(
        HTTPAuthorizationAPI,
        "_make_header",
        return_value={"Authorization": "Bearer token"},
    )
    def test_create_role_distributor_admin(
        self, mocked_make_header, mocked_build_url, mock_validate_role
    ):
        role_request = model.RoleRequest(
            name="TestRole",
            description="Test Description",
            roleGroup=model.RoleGroup.MY_ORGANIZATION,
            permission=["8b376afc-9014-49ff-ad1f-4589e122deda"],
        )
        self.api_client.post.return_value.status_code = 201
        self.api_client.post.return_value.json.return_value = {
            "id": str(UUID("8b376afc-9014-49ff-ad1f-4589e122deda")),
            "name": "TestRole",
            "description": "Test Description",
            "composite": False,
            "clientRole": False,
            "containerId": "master",
            "userCount": 0,
            "created_by": "<EMAIL>",
        }

        response = self.http_authorization.create_role(
            role_request, created_by="<EMAIL>"
        )

        mocked_build_url.assert_called_once_with(self.http_authorization.GET_ROLES)
        mock_validate_role.assert_called_once_with(role_request)
        mocked_make_header.assert_called_once()
        self.api_client.post.assert_called_once_with(
            url="mocked_url",
            headers={"Authorization": "Bearer token"},
            json={
                "attributes": {},
                "name": "TestRole",
                "description": "Test Description",
                "roleGroup": model.RoleGroup.MY_ORGANIZATION,
                "permission": ["8b376afc-9014-49ff-ad1f-4589e122deda"],
            },
        )
        self.assertEqual(
            response,
            model.RoleResponse(
                id=UUID("8b376afc-9014-49ff-ad1f-4589e122deda"),
                name="TestRole",
                description="Test Description",
                composite=False,
                clientRole=False,
                containerId="master",
                userCount=0,
                created_by="<EMAIL>",
            ),
        )

    @patch.object(HTTPAuthorizationAPI, "validate_role", return_value=(True, None))
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    @patch.object(
        HTTPAuthorizationAPI,
        "_make_header",
        return_value={"Authorization": "Bearer token"},
    )
    def test_create_role_distributor_admin_with_group_account(
        self, mocked_make_header, mocked_build_url, mock_validate_role
    ):
        role_request = model.RoleRequest(
            name="TestRole",
            description="Test Description",
            roleGroup=model.RoleGroup.ACCOUNT,
            permission=["8b376afc-9014-49ff-ad1f-4589e122deda"],
        )
        self.api_client.post.return_value.status_code = 201
        self.api_client.post.return_value.json.return_value = {
            "id": str(UUID("8b376afc-9014-49ff-ad1f-4589e122deda")),
            "name": "TestRole",
            "description": "Test Description",
            "composite": False,
            "clientRole": False,
            "containerId": "master",
            "userCount": 0,
            "created_by": "<EMAIL>",
        }

        response = self.http_authorization.create_role(
            role_request, created_by="<EMAIL>"
        )

        mocked_build_url.assert_called_once_with(self.http_authorization.GET_ROLES)
        mock_validate_role.assert_called_once_with(role_request)
        mocked_make_header.assert_called_once()
        self.api_client.post.assert_called_once_with(
            url="mocked_url",
            headers={"Authorization": "Bearer token"},
            json={
                "attributes": {},
                "name": "TestRole",
                "description": "Test Description",
                "roleGroup": model.RoleGroup.ACCOUNT,
                "permission": ["8b376afc-9014-49ff-ad1f-4589e122deda"],
            },
        )
        self.assertEqual(
            response,
            model.RoleResponse(
                id=UUID("8b376afc-9014-49ff-ad1f-4589e122deda"),
                name="TestRole",
                description="Test Description",
                composite=False,
                clientRole=False,
                containerId="master",
                userCount=0,
                created_by="<EMAIL>",
            ),
        )

    @patch.object(HTTPAuthorizationAPI, "validate_role", return_value=(False, None))
    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    @patch.object(
        HTTPAuthorizationAPI,
        "_make_header",
        return_value={"Authorization": "Bearer token"},
    )
    def test_create_role_client_admin(
        self, mocked_make_header, mocked_build_url, mock_validate_role
    ):
        role_request = model.RoleRequest(
            name="TestRole",
            description="Test Description",
            roleGroup=model.RoleGroup.ACCOUNT,
            permission=["8b376afc-9014-49ff-ad1f-4589e122deda"],
        )
        organization_id = 123  # Set the organization ID as needed

        self.api_client.post.return_value.status_code = 201
        self.api_client.post.return_value.json.return_value = {
            "id": str(UUID("8b376afc-9014-49ff-ad1f-4589e122deda")),
            "name": "TestRole",
            "description": "Test Description",
            "composite": False,
            "clientRole": False,
            "containerId": "master",
            "userCount": 0,
            "created_by": "<EMAIL>",
        }

        response = self.http_authorization.create_role(
            role_request, organization_id, "<EMAIL>"
        )

        mocked_build_url.assert_called_once_with(self.http_authorization.GET_ROLES)
        mock_validate_role.assert_called_once_with(role_request)
        mocked_make_header.assert_called_once()
        self.api_client.post.assert_called_once_with(
            url="mocked_url",
            headers={"Authorization": "Bearer token"},
            json={
                "attributes": {},
                "name": "TestRole",
                "description": "Test Description",
                "roleGroup": model.RoleGroup.ACCOUNT,
                "permission": ["8b376afc-9014-49ff-ad1f-4589e122deda"],
            },
        )
        self.assertEqual(
            response,
            model.RoleResponse(
                id=UUID("8b376afc-9014-49ff-ad1f-4589e122deda"),
                name="TestRole",
                description="Test Description",
                composite=False,
                clientRole=False,
                containerId="master",
                userCount=0,
                created_by="<EMAIL>",
            ),
        )

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_is_authorized(self, mocked_build_url):
        scope = "GET/v1/glass/audit/{account_id}"
        self.api_client.post.return_value.json.return_value = {"status": "PERMIT"}
        response = self.http_authorization.is_authorized(scope)
        assert response == "PERMIT"

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_is_authorized_not_found_404(self, mocked_build_url):
        scope = "GET/v1/glass/audit/{account_id}"
        self.api_client.post.side_effect = PlatformAPIError(status_code=404, message="")

        with self.assertRaises(NotFound) as context:
            self.http_authorization.is_authorized(scope)

        self.assertEqual(str(context.exception), "NotFound")
        self.api_client.post.assert_called_once_with(
            url="mocked_url",
            headers={"accept": "application/json", "Content-Type": "application/json"},
            json={"scope": "GET/v1/glass/audit/{account_id}"},
        )

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_is_authorized_forbidden_403(self, mocked_build_url):
        scope = "GET/v1/glass/audit/{account_id}"
        self.api_client.post.side_effect = PlatformAPIError(status_code=403, message="")

        with self.assertRaises(ForbiddenError) as context:
            self.http_authorization.is_authorized(scope)

        self.assertEqual(str(context.exception), "Forbidden")
        self.api_client.post.assert_called_once_with(
            url="mocked_url",
            headers={"accept": "application/json", "Content-Type": "application/json"},
            json={"scope": "GET/v1/glass/audit/{account_id}"},
        )

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_is_authorized_unknown_error(self, mocked_build_url):
        scope = "GET/v1/glass/audit/{account_id}"
        self.api_client.post.side_effect = PlatformAPIError(status_code=500, message="")

        with self.assertRaises(PlatformAPIError) as context:
            self.http_authorization.is_authorized(scope)

        self.assertEqual(str(context.exception), "")
        self.api_client.post.assert_called_once_with(
            url="mocked_url",
            headers={"accept": "application/json", "Content-Type": "application/json"},
            json={"scope": "GET/v1/glass/audit/{account_id}"},
        )

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    @patch.object(
        HTTPAuthorizationAPI,
        "is_authorized",
        side_effect=Exception("General exception raised"),
    )
    def test_is_authorized_general_exception(
        self, mocked_is_authorized, mocked_build_url
    ):
        scope = "GET/v1/glass/audit/{account_id}"

        with pytest.raises(Exception) as e:
            self.http_authorization.is_authorized(scope)

        assert "'General exception raised'" in str(e)
        self.api_client.post.assert_not_called()

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_user_scope(self, mocked_build_url):
        expected_response = model.UserScopeList(
            result=[
                model.UserScope(
                    name="Sim Details",
                    permission=[
                        model.Permission(
                            id=UUID("9a52fa8f-ac1c-433f-95d3-2b13f36efff4"),
                            name="Get SIM Status API",
                            title="Get SIM Status",
                        )
                    ],
                )
            ]
        )
        self.api_client.get.return_value.json.return_value = {
            "result": [
                {
                    "name": "Sim Details",
                    "permission": [
                        {
                            "id": "9a52fa8f-ac1c-433f-95d3-2b13f36efff4",
                            "name": "Get SIM Status API",
                            "title": "Get SIM Status",
                        }
                    ],
                }
            ]
        }
        response = self.http_authorization.get_user_scope()
        assert response == expected_response

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_user_scope_forbidden(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(status_code=403, message="")

        with self.assertRaises(ForbiddenError) as context:
            self.http_authorization.get_user_scope()

        self.assertEqual(str(context.exception), "")
        self.api_client.get.assert_called_once_with(
            url="mocked_url",
        )

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_user_scope_not_found(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(status_code=404, message="")

        with self.assertRaises(NotFound) as context:
            self.http_authorization.get_user_scope()

        self.assertEqual(str(context.exception), "")
        self.api_client.get.assert_called_once_with(
            url="mocked_url",
        )

    @patch.object(HTTPAuthorizationAPI, "build_url", return_value="mocked_url")
    def test_get_user_scope_internal_server_error(self, mocked_build_url):
        self.api_client.get.side_effect = PlatformAPIError(status_code=500, message="")

        with self.assertRaises(Exception) as context:
            self.http_authorization.get_user_scope()

        self.assertEqual(str(context.exception), "")
        self.api_client.get.assert_called_once_with(
            url="mocked_url",
        )


class Group(BaseModel):
    name: str

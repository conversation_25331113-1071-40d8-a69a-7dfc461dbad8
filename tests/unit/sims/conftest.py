import uuid
from datetime import datetime

from common.types import ICCID, IMSI, MSISDN, Month, SimStatus
from rate_plans.domain.model import RatePlan
from sim.adapters.orm import model
from sim.domain.model import (
    Carrier,
    MarketShareIMSI,
    MarketSharePeriod,
    MarketShareUsage,
    MSISDNFactor,
    SimProfile,
    SIMProviderLog,
)

import pytest  # isort: skip


@pytest.fixture
def make_sim_card_model():
    def factory(sim_status=None):
        return model.SIMCard(
            allocation_id=1,
            iccid=ICCID("8944538532046590115"),
            msisdn=MSISDN("***************"),
            imsi=IMSI("***************"),
            sim_status=sim_status,
            rate_plan_id=None,
            sim_profile=SimProfile.DATA_ONLY,
        )

    return factory


@pytest.fixture
def make_pip_sim_status_response():
    def factory(sim_status=SimStatus.ACTIVE):
        return model.SIMStatusResponse(
            reference_id=1,
            imsi=IMSI("***************"),
            msisdn=MSISDN("***************"),
            sim_status=sim_status,
        )

    return factory


@pytest.fixture
def make_monthly_sim_status():
    def factory(sim_status=SimStatus.ACTIVE):
        return model.SIMMonthlyStatus(
            sim_card_id=1,
            month=Month(year=2023, month=7, day=1),
            sim_status=sim_status.READY_FOR_ACTIVATION,
            is_first_activation=True,
        )

    return factory


@pytest.fixture
def make_range():
    def factory():
        return model.Range(
            title="Reference",
            form_factor="MICRO",
            created_at=datetime.now(),
            created_by="John Billing",
            quantity=1,
            imsi_first="************002",
            imsi_last="************002",
            remaining=1,
        )

    return factory


@pytest.fixture
def make_sim_activity_log():
    def factory(prior_value=SimStatus.DEACTIVATED, new_value=SimStatus.ACTIVE):
        return model.SIMActivityLog(
            iccid=ICCID("8944538532046590115"),
            msisdn=MSISDN("***************"),
            imsi=IMSI("***************"),
            request_type="Get status",
            prior_value=prior_value,
            new_value=new_value,
            created_by="sysuser",
            uuid=uuid.UUID("4e26e85b-1077-4993-84a6-826782a290cb"),
            client_ip="127.0.0.1",
        )

    return factory


@pytest.fixture
def make_allocation():
    def factory(id: int):
        return model.Allocation(
            title="Reference",
            account_id=1,
            range_id=1,
            quantity=1,
            id=id,
            created_at=datetime.now(),
            rate_plan_id=1,
            imsi_first=IMSI("************001"),
            imsi_last=IMSI("************001"),
        )

    return factory


@pytest.fixture
def make_sim_card_models():
    def factory(start_with=0, end_with=1, rate_plan_id=None):
        return [
            model.SIMCard(
                iccid=f"*****************{str(i).zfill(3)}",
                imsi=f"************{str(i).zfill(3)}",
                msisdn=f"*********{str(i).zfill(3)}",
                rate_plan_id=rate_plan_id,
                sim_status=SimStatus.DEACTIVATED,
                allocation_id=1,
                sim_profile=SimProfile.DATA_ONLY,
            )
            for i in range(start_with, end_with)
        ]

    return factory


@pytest.fixture
def make_sim_provider_log():
    def factory(work_id, status, audit_date, prior_status="Active"):
        return SIMProviderLog(
            sim_activity_log_uuid="ea2c34bc-afca-4d91-be8b-a77e4829964a",
            activity_id="ea4c11bc-afca-4d91-be8b-a77e4829964a",
            message="Completed",
            prior_status=prior_status,
            work_id=work_id,
            status=status,
            audit_date=audit_date,
        )

    return factory


@pytest.fixture
def make_usage_model():
    def factory(start_with, end_with):
        return [
            model.SimUsage(
                sim_id=i,
                iccid=f"*****************{str(i).zfill(3)}",
                imsi=f"************{str(i).zfill(3)}",
                msisdn=f"*********{str(i).zfill(3)}",
                type="MICRO",
                allocation_reference="test",
                allocation_date=datetime.now(),
                sim_status=SimStatus.DEACTIVATED if i % 2 == 0 else SimStatus.ACTIVE,
                usage="9451",
                rate_plan="test",
                ee_usage="100",
                sim_profile=SimProfile.DATA_ONLY,
                msisdn_factor=MSISDNFactor.INTERNATIONAL,
            )
            for i in range(start_with, end_with)
        ], 1

    return factory


@pytest.fixture
def make_sim_activate_response():
    def factory(sim_status=SimStatus.ACTIVE, message="Queued"):
        return model.SIMActivateResponse(
            uuid="b93a6553-ddf2-4ce4-9a62-e9c588cf4524",
            message=message,
            status=sim_status,
        )

    return factory


@pytest.fixture
def make_sim_deactivate_response():
    def factory(sim_status=SimStatus.DEACTIVATED, message="Queued"):
        return model.SIMDeactivatedResponse(
            uuid="792dad14-7c0a-4639-81fc-6757de3d33a2",
            message=message,
            status=sim_status,
        )

    return factory


@pytest.fixture
def make_auditlog():
    def factory(
        date=datetime.now(),
        new_value=SimStatus.ACTIVE,
        prior_value=SimStatus.DEACTIVATED,
    ):
        return model.AuditLogs(
            field="Status",
            prior_value=prior_value,
            new_value=new_value,
            date=date,
            user_name="sysuser",
        )

    return factory


@pytest.fixture
def make_ConnectionSummary():
    def factory(rate_plan_id=1):
        return model.ConnectionSummary(
            msisdn="********90",
            imsi="********9012345",
            iccid="********90********9",
            first_activated=None,
            last_session=None,
            sim_status=SimStatus.ACTIVE,
            rate_plan_id=rate_plan_id,
            rate_plan="PAYG",
        )

    return factory


@pytest.fixture
def make_rate_plan():
    def factory():
        return RatePlan(
            account_id=1,
            name="Pay as You Go plan",
            access_fee=10.2,
            is_default=False,
            sim_limit=0,
        )

    return factory


@pytest.fixture
def mock_period():
    def factory():
        return MarketSharePeriod(from_date="2023-01-01", to_date="2023-12-31")

    return factory


@pytest.fixture
def mock_market_share_usage():
    def factory():
        carrier_object = Carrier("GBRME")
        carrier_usage_list = [
            MarketShareUsage(carrier=carrier_object, usage=7854),
            MarketShareUsage(carrier=carrier_object, usage=9857),
        ]
        return model.MarketShareCarrier(totalUsage=4875, summary=carrier_usage_list)

    return factory


@pytest.fixture
def mock_sim_card_imsi():
    def factory():
        imsi_object = IMSI("***************")
        return MarketShareIMSI(imsi=imsi_object)

    return factory


@pytest.fixture
def mockMarketShareData():
    def factory():
        return model.MarketShareData(
            account_id=1, from_date="2023-04-01", to_date="2023-04-30"
        )

    return factory


@pytest.fixture
def mockCarrierName():
    def factory():
        return []


@pytest.fixture
def make_voice_connection_models():
    def factory(start_with=0, end_with=1):
        return [
            model.SimVoiceCDRHistory(
                iccid=f"*****************{str(i).zfill(3)}",
                imsi=f"************{str(i).zfill(3)}",
                msisdn=f"*********{str(i).zfill(3)}",
                country="US",
                carrier="GBRME",
                call_date=datetime.now(),
                call_number=********,
                call_minutes=15,
                country_name="United Kingdom",
                carrier_name="EE",
            )
            for i in range(start_with, end_with)
        ]

    return factory


@pytest.fixture
def make_sms_connection_models():
    def factory(start_with=0, end_with=1):
        return [
            model.SimSMSCDRHistory(
                iccid=f"*****************{str(i).zfill(3)}",
                imsi=f"889459000000{str(i).zfill(3)}",
                msisdn=f"*********{str(i).zfill(3)}",
                country="US",
                carrier="GBRME",
                date_sent=datetime.now(),
                sent_from=********,
                sent_to=12345679,
                country_name="United Kingdom",
                carrier_name="EE",
            )
            for i in range(start_with, end_with)
        ]

    return factory


@pytest.fixture
def make_custom_models():
    def factory(
        start_with=0,
        end_with=1,
    ):
        return [
            model.CustomModel(
                imsi=f"************{str(i).zfill(3)}",
                form_factor="STANDARD",
                allocation_id=1,
            )
            for i in range(start_with, end_with)
        ]

    return factory


@pytest.fixture
def mock_CarrierName():
    def factory():
        return model.CarrierName(carrier="GBRME", carrier_name="OS")

    return factory


@pytest.fixture
def mock_market_share_usage_with_null_summary():
    def factory():
        return model.MarketShareCarrier(totalUsage=4875, summary=[])

    return factory


@pytest.fixture
def create_rate_plan():
    def factory(**kwargs):
        RatePlan = {"account_id": 1, "name": "test", "access_fee": 0.2}
        return RatePlan

    return factory

from collections import namedtuple
from decimal import Decimal
from unittest.mock import MagicMock
from uuid import UUID

from api.automation import schemas
from api.automation.examples import (
    ACTION_MODEL,
    NOTIFICATION_MODEL,
    RULE_BY_UUID,
    RULE_BY_UUID_RULE_CATEGORY,
    RULE_BY_UUID_RULE_DEFINITIION,
    RULE_BY_UUID_RULE_TYPE,
    RULE_DETAIL_1,
    RULE_DETAIL_MODEL,
    RULE_REQUEST,
    RULE_RESPONSE,
)
from api.rate_plans.examples import CREATE_UPDATE_RATE_MODEL, GET_RATE_PLAN_MODELS
from authorization.domain.ports import AbstractAuthorization<PERSON><PERSON>
from automation.adapters.repository import AbstractAutomationRepository
from automation.domain import model
from automation.exceptions import RuleDetailsNotFound, RuleError
from automation.services import AutomationService
from common.constants import FIXED, FLEXI
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import Unit
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    DatabaseRatePlanRepository,
)
from rate_plans.domain.model import (
    Rate,
    RateGroup,
    RateModel,
    RatePlan,
    RatePlanModels,
    Service,
)
from rate_plans.exceptions import RatePlanError
from rate_plans.services import MediaService, RatePlanService
from sim.adapters.repository import AbstractSimRepository

import pytest  # isort: skip


@pytest.fixture
def automation_service():
    mock_automation_repository = MagicMock(spec=AbstractAutomationRepository)
    mock_automation_service = AutomationService(
        automation_repository=mock_automation_repository
    )

    return mock_automation_service


@pytest.fixture
def automation_service_mock():
    mock_automation_repository = MagicMock(spec=AbstractAutomationRepository)
    mock_automation_repository.create_rule.return_value = True
    mock_automation_repository.create_rules_notification.return_value = True
    mock_automation_repository.create_rules_action.return_value = model.RuleDetails(
        **RULE_RESPONSE
    )
    mock_automation_repository.get_rule_by_uuid.return_value = model.RuleData(
        **RULE_BY_UUID
    )
    mock_automation_repository.get_actions_by_rule_uuid.return_value = [ACTION_MODEL]
    mock_automation_repository.get_notification_by_rule_uuid.return_value = [
        NOTIFICATION_MODEL
    ]
    mock_automation_repository.get_rule.return_value = [
        model.RulesData(**RULE_DETAIL_1)
    ]

    mock_automation_service = AutomationService(
        automation_repository=mock_automation_repository
    )

    return mock_automation_service


@pytest.fixture
def mock_rate_plan():
    return RatePlan(
        _id=202,
        account_id=1,
        name="FIXED 202501",
        access_fee=Decimal("12.03"),
        is_default=False,
        sim_limit=100,
        _rate_groups=[
            RateGroup(
                rates=[
                    Rate(
                        range_from=0,
                        range_to=10240,
                        value=Decimal("12.03"),
                        range_unit="Min",
                        price_unit="Min",
                        overage_fee=Decimal("5.26"),
                        overage_unit="Min",
                        isoverage=True,
                        overage_per=60,
                    )
                ],
                rate_model_id=2,
                services={Service.VOICE_MO},
            ),
            RateGroup(
                rates=[
                    Rate(
                        range_from=0,
                        range_to=10241,
                        value=Decimal("3.24"),
                        range_unit="Min",
                        price_unit="Min",
                        overage_fee=Decimal("6.27"),
                        overage_unit="Min",
                        isoverage=False,
                        overage_per=60,
                    )
                ],
                rate_model_id=3,
                services={Service.VOICE_MT},
            ),
            RateGroup(
                rates=[
                    Rate(
                        range_from=0,
                        range_to=10242,
                        value=Decimal("12.03"),
                        range_unit="SMS",
                        price_unit="SMS",
                        overage_fee=Decimal("7.28"),
                        overage_unit="SMS",
                        isoverage=True,
                        overage_per=100,
                    )
                ],
                rate_model_id=4,
                services={Service.SMS_MO},
            ),
            RateGroup(
                rates=[
                    Rate(
                        range_from=0,
                        range_to=500,
                        value=Decimal("12.03"),
                        range_unit="KB",
                        price_unit="KB",
                        overage_fee=Decimal("5.26"),
                        overage_unit="KB",
                        isoverage=False,
                        overage_per=5,
                    )
                ],
                rate_model_id=3,
                services={Service.DATA},
            ),
        ],
    )


@pytest.fixture
def mock_rate_plan_service(mock_rate_plan):
    mock_rate_plan_repository = MagicMock(spec=AbstractRatePlanRepository)
    mock_media_service = MagicMock(spec=MediaService)
    mock_sim_repository = MagicMock(spec=AbstractSimRepository)

    rate_models = list(
        map(lambda rate_model: RateModel(**rate_model), GET_RATE_PLAN_MODELS)
    )
    mock_rate_plan_repository.get_rate_plans_models.return_value = RatePlanModels(
        result=rate_models
    )
    mock_rate_plan_repository.get_rate_plans_model_by_id.return_value = rate_models[1]

    create_updateed_rate_model = RateModel(**CREATE_UPDATE_RATE_MODEL)
    mock_rate_plan_repository.update_rate_plans_model_by_id.return_value = (
        create_updateed_rate_model
    )
    mock_rate_plan_repository.create_rate_plan_model.return_value = (
        create_updateed_rate_model
    )
    mock_rate_plan_repository.delete_rate_plan_model.return_value = None

    mock_sim_repository.allocation_count_by_account.return_value = 5
    mock_sim_repository.get_allocation_count_by_rate_plan.return_value = 5

    mock_rate_plan_repository.unset_default_rate_plan.return_value = None
    mock_rate_plan_repository.set_default_rate_plan.return_value = None
    mock_rate_plan_repository.get.return_value = mock_rate_plan

    mock_media_service.get_file_url.return_value = "https://example.com/logo.png"

    rate_plan_service_mock = RatePlanService(
        rate_plan_repository=mock_rate_plan_repository,
        media_service=mock_media_service,
        sim_repository=mock_sim_repository,
    )
    return rate_plan_service_mock


@pytest.fixture
def automation_service_mock_error():
    def factory(error_type, message):
        mock_automation_repository = MagicMock(spec=AbstractAutomationRepository)
        mock_automation_repository.create_rule.return_value = model.RuleDetails(
            **RULE_RESPONSE
        )
        mock_automation_repository.create_rule.side_effect = error_type(message)
        mock_automation_repository.get_rule.return_value = []
        mock_automation_repository.get_actions_by_rule_uuid.return_value = [
            ACTION_MODEL
        ]
        mock_automation_repository.get_notification_by_rule_uuid.return_value = [
            NOTIFICATION_MODEL
        ]

        mock_automation_service = AutomationService(
            automation_repository=mock_automation_repository
        )

        return mock_automation_service

    return factory


class AutomationContract:
    @pytest.fixture
    def automation_repository(self, *args, **kwargs) -> AbstractAutomationRepository:
        raise NotImplementedError()

    @pytest.fixture
    def automation_service(
        self,
        automation_repository: AbstractAutomationRepository,
    ) -> AutomationService:
        return AutomationService(
            automation_repository=automation_repository,
        )


@pytest.fixture
def order_search_pagination_mock():
    mock_ordering = MagicMock()
    mock_searching = MagicMock()
    mock_pagination = MagicMock()

    mock_ordering.field = "name"
    mock_ordering.order = "ASC"
    mock_searching.field = "name"
    mock_searching.search = "Test"
    mock_pagination.page = 1
    mock_pagination.page_size = 50

    return mock_ordering, mock_searching, mock_pagination


@pytest.fixture
def mock_authorization():
    """Mock authorization service."""
    return MagicMock(spec=AbstractAuthorizationAPI)


@pytest.fixture
def mock_rate_service():
    return MagicMock(spec=RatePlanService)


class TestAutomation:
    def setup_method(self):
        self.automation = AutomationService(AbstractAutomationRepository)

    def test_create_rule_success(self, automation_service_mock):

        # model_response = model.RuleDetails(**RULE_RESPONSE)
        # expected_response = model.CreateRuleDetails(rules_uuid=[model_response])
        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)

        rules_detail = create_rule_request.to_model()
        scope = "PATCH/v1/glass/rule/{rule_uuid}/lock/{lock_status}"
        result = automation_service_mock.create_rule(
            rules_detail=rules_detail, scope=scope, authorization=mock_authorization
        )

        assert isinstance(result, model.CreateRuleDetails)
        # assert result == expected_response

    def test_create_rule_error(self, automation_service_mock_error):

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        rules_detail = create_rule_request.to_model()

        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleError,
            message="Create rule error.:",
        )
        scope = "PATCH/v1/glass/rule/{rule_uuid}/lock/{lock_status}"
        with pytest.raises(RuleError):
            automation_service_mock_data.create_rule(
                rules_detail=rules_detail, scope=scope, authorization=mock_authorization
            )

    def test_create_MPDU_rule_success(self, automation_service_mock):
        create_rule_request = schemas.CreateRuleRequest(
            **{
                "accountId": [1],
                "ruleTypeId": 1,
                "ruleCategoryId": 4,
                "ruleDefinitionId": 1,
                "ruleName": "Rule1",
                "dataVolume": 99,
                "unit": Unit.PERCENTAGE,
                "status": True,
                "action": {
                    "reactivateSim": {"value": "Next billing cycle"},
                },
                "notification": [],
                "lock": False,
                "ruleType": RULE_BY_UUID_RULE_TYPE,
                "ruleCategory": RULE_BY_UUID_RULE_CATEGORY,
                "ruleDefinition": RULE_BY_UUID_RULE_DEFINITIION,
            }
        )

        rules_detail = create_rule_request.to_model()
        scope = "PATCH/v1/glass/rule/{rule_uuid}/lock/{lock_status}"
        result = automation_service_mock.create_rule(
            rules_detail=rules_detail, scope=scope, authorization=mock_authorization
        )

        assert isinstance(result, model.CreateRuleDetails)

    def test_get_rule_by_uuid_success(self, automation_service_mock):
        result = automation_service_mock.get_rule_by_uuid(
            UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea")
        )
        RULE_DETAIL_MODEL.rule_type = RULE_BY_UUID_RULE_TYPE
        RULE_DETAIL_MODEL.rule_category = RULE_BY_UUID_RULE_CATEGORY
        RULE_DETAIL_MODEL.rule_definition = RULE_BY_UUID_RULE_DEFINITIION
        assert result == RULE_DETAIL_MODEL
        assert isinstance(result, model.RuleDetailsResponse)

    def test_get_rule_success(
        self, automation_service_mock, order_search_pagination_mock
    ):
        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock

        result, rule_count = automation_service_mock.get_rule(
            ordering=mock_ordering,
            searching=mock_searching,
            pagination=mock_pagination,
        )
        assert isinstance(result, model.RuleDetailResponseList)

    def test_get_rule_error(
        self, automation_service_mock_error, order_search_pagination_mock
    ):

        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock

        automation_service_mock_data = automation_service_mock_error(
            error_type=RuleDetailsNotFound,
            message="No rules found",
        )

        with pytest.raises(RuleDetailsNotFound):
            automation_service_mock_data.get_rule(
                ordering=mock_ordering,
                searching=mock_searching,
                pagination=mock_pagination,
            )

    def test_rule_type(self, make_rule_type_models):
        category_model = make_rule_type_models()
        self.automation.automation_repository.rule_type = MagicMock(
            return_value=iter(category_model)
        )
        self.automation.automation_repository.rule_type_count = MagicMock(
            return_value=len(category_model)
        )
        result = self.automation.get_rule_type()
        self.automation.automation_repository.rule_type.assert_called_once()
        assert isinstance(result, tuple)
        assert len(result) == 2
        assert list(result[0]) == category_model

    def test_get_actions_no_filters(self, make_action_models):
        action_models = make_action_models()
        self.automation.automation_repository.get_actions = MagicMock(
            return_value=iter(action_models)
        )

        self.automation.automation_repository.get_actions_count = MagicMock(
            return_value=len(action_models)
        )

        action_data = self.automation.get_actions()
        self.automation.automation_repository.get_actions.assert_called_once_with(
            None, None, None, None
        )
        assert list(action_data) == action_models

    def test_get_actions_with_action_id(self, make_action_models):
        action_id = 1
        action_models = make_action_models()
        self.automation.automation_repository.get_actions = MagicMock(
            return_value=iter(action_models)
        )
        self.automation.automation_repository.get_actions_count = MagicMock(
            return_value=len(action_models)
        )
        result = self.automation.get_actions(action_id=action_id)
        self.automation.automation_repository.get_actions.assert_called_once_with(
            action_id, None, None, None
        )
        assert list(result) == action_models

    def test_get_actions_with_pagination(self, make_action_models):
        pagination = Pagination(page=1, page_size=50)
        action_models = make_action_models()
        self.automation.automation_repository.get_actions = MagicMock(
            return_value=iter(action_models)
        )
        self.automation.automation_repository.get_actions_count = MagicMock(
            return_value=len(action_models)
        )
        result = self.automation.get_actions(pagination=pagination)
        self.automation.automation_repository.get_actions.assert_called_once_with(
            None, pagination, None, None
        )
        assert list(result) == action_models

    def test_get_actions_with_ordering(self, make_action_models):
        ordering = Ordering(field="action", order="ASC")
        action_models = make_action_models()
        self.automation.automation_repository.get_actions = MagicMock(
            return_value=iter(action_models)
        )
        self.automation.automation_repository.get_actions_count = MagicMock(
            return_value=len(action_models)
        )
        result = self.automation.get_actions(ordering=ordering)
        self.automation.automation_repository.get_actions.assert_called_once_with(
            None, None, ordering, None
        )
        assert list(result) == action_models

    def test_get_actions_with_searching(self, make_action_models):
        searching = MagicMock(spec=Searching)
        action_models = make_action_models()
        self.automation.automation_repository.get_actions = MagicMock(
            return_value=iter(action_models)
        )
        self.automation.automation_repository.get_actions_count = MagicMock(
            return_value=len(action_models)
        )
        result = self.automation.get_actions(searching=searching)
        self.automation.automation_repository.get_actions.assert_called_once_with(
            None, None, None, searching
        )
        assert list(result) == action_models

    def test_get_actions_count(self, make_action_models):
        action_models = make_action_models()
        self.automation.automation_repository.get_actions_count = MagicMock(
            return_value=len(action_models)
        )
        count = self.automation.get_actions_count()
        self.automation.automation_repository.get_actions_count.assert_called_once_with(  # noqa
            None, None
        )
        assert count == len(action_models)

    def test_get_actions_count_with_action_id(self, make_action_models):
        action_id = 1
        action_models = make_action_models()
        self.automation.automation_repository.get_actions_count = MagicMock(
            return_value=len(action_models)
        )
        count = self.automation.get_actions_count(action_id=action_id)
        self.automation.automation_repository.get_actions_count.assert_called_once_with(  # noqa
            action_id, None
        )
        assert count == len(action_models)

    def test_get_actions_count_with_searching(self, make_action_models):
        searching = MagicMock(spec=Searching)
        action_models = make_action_models()
        self.automation.automation_repository.get_actions_count = MagicMock(
            return_value=len(action_models)
        )
        count = self.automation.get_actions_count(searching=searching)
        self.automation.automation_repository.get_actions_count.assert_called_once_with(  # noqa
            None, searching
        )
        assert count == len(action_models)

    def test_get_actions_count_with_action_id_and_searching(self, make_action_models):
        searching = MagicMock(spec=Searching)
        action_id = 1
        action_models = make_action_models()
        self.automation.automation_repository.get_actions_count = MagicMock(
            return_value=len(action_models)
        )
        count = self.automation.get_actions_count(
            action_id=action_id, searching=searching
        )
        self.automation.automation_repository.get_actions_count.assert_called_once_with(  # noqa
            action_id, searching
        )
        assert count == len(action_models)

    def test_get_notification(self, make_notification_models):
        notification_models = make_notification_models()
        self.automation.automation_repository.get_notification_list = MagicMock(
            return_value=iter(notification_models)
        )
        result = self.automation.get_notification()
        assert list(result) == notification_models

    def test_get_notification_with_notification_id(self, make_notification_models):
        notification_id = 1
        notification_models = make_notification_models()
        self.automation.automation_repository.get_notification_list = MagicMock(
            return_value=iter(notification_models)
        )
        result = self.automation.get_notification(notification_id=notification_id)
        assert list(result) == notification_models

    def test_get_notification_with_pagination(self, make_notification_models):
        pagination = Pagination(page=1, page_size=50)
        notification_models = make_notification_models()
        self.automation.automation_repository.get_notification_list = MagicMock(
            return_value=iter(notification_models)
        )
        result = self.automation.get_notification(pagination=pagination)
        assert list(result) == notification_models

    def test_get_notification_with_searching(self, make_notification_models):
        searching = MagicMock(spec=Searching)
        notification_models = make_notification_models()
        self.automation.automation_repository.get_notification_list = MagicMock(
            return_value=iter(notification_models)
        )
        result = self.automation.get_notification(searching=searching)
        assert list(result) == notification_models

    def test_get_notification_count(self, make_notification_models):
        notification_models = make_notification_models()
        self.automation.automation_repository.get_notification_count = MagicMock(
            return_value=len(notification_models)
        )
        count = self.automation.get_notification_count()
        self.automation.automation_repository.get_notification_count.assert_called_once_with(  # noqa
            notification_id=None, searching=None
        )
        assert count == len(notification_models)

    def test_get_notification_count_with_notification_id(
        self, make_notification_models
    ):
        notification_id = 1
        notification_models = make_notification_models()
        self.automation.automation_repository.get_notification_count = MagicMock(
            return_value=len(notification_models)
        )
        count = self.automation.get_notification_count(notification_id=notification_id)
        self.automation.automation_repository.get_notification_count.assert_called_once_with(  # noqa
            notification_id=notification_id, searching=None
        )
        assert count == len(notification_models)

    def test_get_notification_count_with_searching(self, make_notification_models):
        searching = MagicMock(spec=Searching)
        notification_models = make_notification_models()
        self.automation.automation_repository.get_notification_count = MagicMock(
            return_value=len(notification_models)
        )
        count = self.automation.get_notification_count(searching=searching)
        self.automation.automation_repository.get_notification_count.assert_called_once_with(  # noqa
            notification_id=None, searching=searching
        )
        assert count == len(notification_models)

    def test_get_notification_count_with_notification_id_and_searching(
        self, make_notification_models
    ):
        searching = MagicMock(spec=Searching)
        notification_id = 1
        notification_models = make_notification_models()
        self.automation.automation_repository.get_notification_count = MagicMock(
            return_value=len(notification_models)
        )
        count = self.automation.get_notification_count(
            notification_id=notification_id, searching=searching
        )
        self.automation.automation_repository.get_notification_count.assert_called_once_with(  # noqa
            notification_id=notification_id, searching=searching
        )
        assert count == len(notification_models)

    def test_check_lock_permission_permit(self, mock_authorization):
        """Test case where permission is PERMIT, should return True."""
        mock_authorization.is_authorized.return_value = "PERMIT"
        scope = "PATCH/v1/glass/rule/{rule_uuid}/lock/{lock_status}"
        result = self.automation.check_permission(
            scope=scope,
            authorization=mock_authorization,
        )
        assert result is True

    def test_check_lock_permission_deny(self, mock_authorization):
        """Test case where permission is PERMIT, should return True."""
        mock_authorization.is_authorized.return_value = "DENY"
        scope = "PATCH/v1/glass/rule/{rule_uuid}/lock/{lock_status}"
        result = self.automation.check_permission(
            scope=scope,
            authorization=mock_authorization,
        )
        assert result is False

    def test_validate_rate_plan_with_target_only(self, mock_rate_plan_service):
        rules = [
            model.Rules(
                uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                account_id=1,
                rule_type_id=1,
                rule_category_id=1,
                rule_definition_id=1,
                data_volume=1,
                status=True,
                lock=True,
                actions=[
                    model.RulesAction(
                        action=model.RuleAction.CHANGE_RATE_PLAN,
                        action_value="Change rate plan",
                        actions_id=1,
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        target=2,
                    )
                ],
                notifications=[
                    model.RulesNotification(
                        notifications_id=1,
                        notification=True,
                        notification_value=[
                            model.NotificationValue(
                                notification_value="Notification value",
                                rules_notification_id=1,
                            )
                        ],
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        id=1,
                    )
                ],
                unit=Unit.MB,
                rule_name="Rule name",
                created_by="Created by",
                ip_address="IP address",
            )
        ]

        rule_category = {
            "rule_type_id": 1,
            "id": 101,
            "category": "Test Category",
            "rule_type": "Usage Monitoring",
            "rule_category_code": "CTDDU",
        }
        self.automation.automation_repository.rule_category_by_id = MagicMock(
            return_value=model.RuleCategoryDetails(**rule_category)
        )
        mock_rate_plan_service.get_account_rate_plan = MagicMock()

        result = self.automation.validate_rate_plan(
            rate_service=mock_rate_plan_service, rules_detail=rules
        )

        for rule in rules:
            for action in rule.actions:
                if action.action == model.RuleAction.CHANGE_RATE_PLAN:
                    mock_rate_plan_service.get_account_rate_plan.assert_called_once_with(  # noqa
                        rule.account_id, [action.target]
                    )
        assert result is True

    def test_validate_rate_plan_with_source_and_target(self, mock_rate_plan_service):
        rules = [
            model.Rules(
                uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                account_id=1,
                rule_type_id=1,
                rule_category_id=1,
                rule_definition_id=1,
                data_volume=1,
                status=True,
                lock=True,
                actions=[
                    model.RulesAction(
                        action=model.RuleAction.CHANGE_RATE_PLAN,
                        action_value="Change rate plan",
                        actions_id=1,
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        source=1,
                        target=2,
                    )
                ],
                notifications=[
                    model.RulesNotification(
                        notifications_id=1,
                        notification=True,
                        notification_value=[
                            model.NotificationValue(
                                notification_value="Notification value",
                                rules_notification_id=1,
                            )
                        ],
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        id=1,
                    )
                ],
                unit=Unit.MB,
                rule_name="Rule name",
                created_by="Created by",
                ip_address="IP address",
            )
        ]

        rule_category = {
            "rule_type_id": 1,
            "id": 101,
            "category": "Test Category",
            "rule_type": "Usage Monitoring",
            "rule_category_code": "TC",
        }
        self.automation.automation_repository.rule_category_by_id = MagicMock(
            return_value=model.RuleCategoryDetails(**rule_category)
        )
        mock_rate_plan_service.get_account_rate_plan = MagicMock()

        result = self.automation.validate_rate_plan(
            rate_service=mock_rate_plan_service, rules_detail=rules
        )

        for rule in rules:
            for action in rule.actions:
                if action.action == model.RuleAction.CHANGE_RATE_PLAN:
                    mock_rate_plan_service.get_account_rate_plan.assert_called_once_with(  # noqa
                        rule.account_id, [action.source, action.target]
                    )
        assert result is True

    def test_validate_rate_plan_with_no_change_rate_plan_action(
        self, mock_rate_plan_service
    ):
        rules = [
            model.Rules(
                uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                account_id=1,
                rule_type_id=1,
                rule_category_id=1,
                rule_definition_id=1,
                data_volume=1,
                status=True,
                lock=True,
                actions=[],
                notifications=[
                    model.RulesNotification(
                        notifications_id=1,
                        notification=True,
                        notification_value=[
                            model.NotificationValue(
                                notification_value="Notification value",
                                rules_notification_id=1,
                            )
                        ],
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        id=1,
                    )
                ],
                unit=Unit.MB,
                rule_name="Rule name",
                created_by="Created by",
                ip_address="IP address",
            )
        ]

        rule_category = {
            "rule_type_id": 1,
            "id": 101,
            "category": "Test Category",
            "rule_type": "Usage Monitoring",
            "rule_category_code": "TC",
        }
        self.automation.automation_repository.rule_category_by_id = MagicMock(
            return_value=model.RuleCategoryDetails(**rule_category)
        )
        mock_rate_plan_service.get_account_rate_plan = MagicMock()

        result = self.automation.validate_rate_plan(
            rate_service=mock_rate_plan_service, rules_detail=rules
        )

        mock_rate_plan_service.get_account_rate_plan.assert_not_called()
        assert result is True

    def test_validate_rate_plan_with_no_actions(self, mock_rate_plan_service):
        rules = [
            model.Rules(
                uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                account_id=1,
                rule_type_id=1,
                rule_category_id=1,
                rule_definition_id=1,
                data_volume=1,
                status=True,
                lock=True,
                actions=[],
                notifications=[
                    model.RulesNotification(
                        notifications_id=1,
                        notification=True,
                        notification_value=[
                            model.NotificationValue(
                                notification_value="Notification value",
                                rules_notification_id=1,
                            )
                        ],
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        id=1,
                    )
                ],
                unit=Unit.MB,
                rule_name="Rule name",
                created_by="Created by",
                ip_address="IP address",
            )
        ]

        rule_category = {
            "rule_type_id": 1,
            "id": 101,
            "category": "Test Category",
            "rule_type": "Usage Monitoring",
            "rule_category_code": "TC",
        }
        self.automation.automation_repository.rule_category_by_id = MagicMock(
            return_value=model.RuleCategoryDetails(**rule_category)
        )
        mock_rate_plan_service.get_account_rate_plan = MagicMock()

        result = self.automation.validate_rate_plan(
            rate_service=mock_rate_plan_service, rules_detail=rules
        )

        mock_rate_plan_service.get_account_rate_plan.assert_not_called()
        assert result is True

    def test_validate_rate_plan_with_source_target_MPDU(self, mock_rate_plan_service):

        mock_db_session = MagicMock()
        MockRow = namedtuple(
            "MockRow",
            ["rate_plan_id", "rate_model_id", "model", "rate_model_code", "service"],
        )
        mock_db_session.execute.return_value.all.return_value = [
            MockRow(
                rate_plan_id=1,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="DATA",
            ),
            MockRow(
                rate_plan_id=2,
                rate_model_id=4,
                model="Flexible Plan",
                rate_model_code=FLEXI,
                service="DATA",
            ),
            MockRow(
                rate_plan_id=3,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="DATA",
            ),
        ]
        DatabaseRatePlanRepository(session=mock_db_session)

        rules = [
            model.Rules(
                uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                account_id=1,
                rule_type_id=1,
                rule_category_id=4,
                rule_definition_id=1,
                data_volume=1,
                status=True,
                lock=True,
                actions=[
                    model.RulesAction(
                        action=model.RuleAction.CHANGE_RATE_PLAN,
                        action_value="Change rate plan",
                        actions_id=1,
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        source=1,
                        target=2,
                    )
                ],
                notifications=[
                    model.RulesNotification(
                        notifications_id=1,
                        notification=True,
                        notification_value=[
                            model.NotificationValue(
                                notification_value="Notification value",
                                rules_notification_id=1,
                            )
                        ],
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        id=1,
                    )
                ],
                unit=Unit.PERCENTAGE,
                rule_name="Rule name",
                created_by="Created by",
                ip_address="IP address",
            )
        ]

        rule_category = {
            "rule_type_id": 1,
            "id": 101,
            "category": "Test Category",
            "rule_type": "Usage Monitoring",
            "rule_category_code": "MPDU",
        }

        self.automation.automation_repository.rule_category_by_id = MagicMock(
            return_value=model.RuleCategoryDetails(**rule_category)
        )
        mock_rate_plan_service.get_account_rate_plan = MagicMock()

        result = self.automation.validate_rate_plan(
            rate_service=mock_rate_plan_service, rules_detail=rules
        )

        for rule in rules:
            for action in rule.actions:
                if action.action == model.RuleAction.CHANGE_RATE_PLAN:
                    mock_rate_plan_service.get_account_rate_plan.assert_called_once_with(  # noqa
                        rule.account_id, [action.source, action.target]
                    )
        assert result is True

    def test_validate_rate_plan_without_source_target_MPDU(
        self, mock_rate_plan_service
    ):

        mock_db_session = MagicMock()
        MockRow = namedtuple(
            "MockRow",
            ["rate_plan_id", "rate_model_id", "model", "rate_model_code", "service"],
        )
        mock_db_session.execute.return_value.all.return_value = [
            MockRow(
                rate_plan_id=1,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="DATA",
            ),
            MockRow(
                rate_plan_id=2,
                rate_model_id=4,
                model="Flexible Plan",
                rate_model_code=FLEXI,
                service="DATA",
            ),
            MockRow(
                rate_plan_id=3,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="DATA",
            ),
        ]
        DatabaseRatePlanRepository(session=mock_db_session)

        rules = [
            model.Rules(
                uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                account_id=1,
                rule_type_id=1,
                rule_category_id=4,
                rule_definition_id=1,
                data_volume=1,
                status=True,
                lock=True,
                actions=[],
                notifications=[
                    model.RulesNotification(
                        notifications_id=1,
                        notification=True,
                        notification_value=[
                            model.NotificationValue(
                                notification_value="Notification value",
                                rules_notification_id=1,
                            )
                        ],
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        id=1,
                    )
                ],
                unit=Unit.PERCENTAGE,
                rule_name="Rule name",
                created_by="Created by",
                ip_address="IP address",
            )
        ]

        rule_category = {
            "rule_type_id": 1,
            "id": 101,
            "category": "Test Category",
            "rule_type": "Usage Monitoring",
            "rule_category_code": "MPDU",
        }

        self.automation.automation_repository.rule_category_by_id = MagicMock(
            return_value=model.RuleCategoryDetails(**rule_category)
        )
        mock_rate_plan_service.get_account_rate_plan = MagicMock()

        result = self.automation.validate_rate_plan(
            rate_service=mock_rate_plan_service, rules_detail=rules
        )

        for rule in rules:
            for action in rule.actions:
                if action.action == model.RuleAction.CHANGE_RATE_PLAN:
                    mock_rate_plan_service.get_account_rate_plan.assert_called_once_with(  # noqa
                        rule.account_id, [action.source, action.target]
                    )
        assert result is True

    def test_validate_rate_plan_with_source_target_MPDU_unit_assertion(
        self, mock_rate_plan_service
    ):

        mock_db_session = MagicMock()
        MockRow = namedtuple(
            "MockRow",
            ["rate_plan_id", "rate_model_id", "model", "rate_model_code", "service"],
        )
        mock_db_session.execute.return_value.all.return_value = [
            MockRow(
                rate_plan_id=1,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="DATA",
            ),
            MockRow(
                rate_plan_id=2,
                rate_model_id=4,
                model="Flexible Plan",
                rate_model_code=FLEXI,
                service="DATA",
            ),
            MockRow(
                rate_plan_id=3,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="DATA",
            ),
        ]
        DatabaseRatePlanRepository(session=mock_db_session)

        rules = [
            model.Rules(
                uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                account_id=1,
                rule_type_id=1,
                rule_category_id=4,
                rule_definition_id=1,
                data_volume=1,
                status=True,
                lock=True,
                actions=[
                    model.RulesAction(
                        action=model.RuleAction.CHANGE_RATE_PLAN,
                        action_value="Change rate plan",
                        actions_id=1,
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        source=1,
                        target=2,
                    )
                ],
                notifications=[
                    model.RulesNotification(
                        notifications_id=1,
                        notification=True,
                        notification_value=[
                            model.NotificationValue(
                                notification_value="Notification value",
                                rules_notification_id=1,
                            )
                        ],
                        rules_uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
                        id=1,
                    )
                ],
                unit=Unit.KB,
                rule_name="Rule name",
                created_by="Created by",
                ip_address="IP address",
            )
        ]

        rule_category = {
            "rule_type_id": 1,
            "id": 101,
            "category": "Test Category",
            "rule_type": "Usage Monitoring",
            "rule_category_code": "MPDU",
        }

        self.automation.automation_repository.rule_category_by_id = MagicMock(
            return_value=model.RuleCategoryDetails(**rule_category)
        )
        mock_rate_plan_service.get_account_rate_plan = MagicMock()

        with pytest.raises(RatePlanError):
            self.automation.validate_rate_plan(
                rate_service=mock_rate_plan_service, rules_detail=rules
            )

    def test_get_rule_rely(self):
        mock_rule_rely = model.RuleResponseRely(
            id=1,
            rules=model.RulesRely(
                data_volume=True,
                data_unit=True,
                sms_unit=False,
                voice_unit=False,
                threshold=True,
                percentage_unit=False,
            ),
            action=model.ActionRely(
                view_deactivate_sim=True,
                required_deactivate_sim=False,
                view_rate_plan_change=True,
                required_rate_plan_change=False,
                add_any_rate_plan=True,
                is_monthly_pool=True,
            ),
            notification=model.NotificationRely(
                view_email=True, view_sms=False, view_push=False
            ),
        )

        self.automation.automation_repository.get_rule_rely = MagicMock(
            return_value=mock_rule_rely
        )
        result = self.automation.get_rule_rely(rule_category_id=1)

        assert result == mock_rule_rely

    def test_create_rule_rely_success(self):
        """Test case when create_rule_rely succeeds"""
        mock_rule_rely = model.RuleDetailsRely(
            rule_category_id=1,
            data_unit=True,
            data_volume=True,
            sms_unit=False,
            voice_unit=False,
            threshold=False,
            percentage_unit=False,
            view_deactivate_sim=True,
            required_deactivate_sim=False,
            view_rate_plan_change=True,
            required_rate_plan_change=False,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=True,
            view_sms=False,
            view_push=False,
        )

        self.automation.automation_repository.create_rule_rely = MagicMock(
            return_value=True
        )

        result = self.automation.create_rule_rely(rule_rely=mock_rule_rely)

        assert result is True

    def test_update_rule_rely_success(self):
        """Test successful update of rule rely"""
        mock_rule_rely = model.RuleDetailsRely(
            rule_category_id=1,
            data_unit=True,
            data_volume=True,
            sms_unit=False,
            voice_unit=False,
            threshold=False,
            percentage_unit=False,
            view_deactivate_sim=True,
            required_deactivate_sim=False,
            view_rate_plan_change=True,
            required_rate_plan_change=False,
            add_any_rate_plan=True,
            is_monthly_pool=True,
            view_email=True,
            view_sms=False,
            view_push=False,
        )
        self.automation.automation_repository.get_rule_rely_by_id = MagicMock(
            return_value=mock_rule_rely
        )
        self.automation.update_rule_rely = MagicMock(return_value=True)

        result = self.automation.update_rule_rely(mock_rule_rely, rule_rely_id=1)
        assert result is True

import random
import string
from collections import namedtuple
from unittest.mock import MagicMock
from uuid import UUI<PERSON>, uuid4

from sqlalchemy import func, literal_column, select
from sqlalchemy.exc import IntegrityError

from api.automation import schemas
from api.automation.examples import (
    RULE_BY_UUID_RULE_CATEGORY,
    RULE_BY_UUID_RULE_DEFINITIION,
    RULE_BY_UUID_RULE_TYPE,
    RULE_DETAIL_1,
    RULE_REQUEST,
)
from automation.adapters import orm
from automation.adapters.exceptions import RuleNotFound
from automation.adapters.repository import DatabaseAutomationRepository
from automation.domain import model
from automation.domain.model import RulesData
from automation.exceptions import CreateRuleError, RuleAlreadyExist
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching

import pytest  # isort: skip


@pytest.fixture
def db_session():
    session_mock = MagicMock()
    mock_repository = DatabaseAutomationRepository(session_mock)
    return session_mock, mock_repository


@pytest.fixture
def make_action_data():
    action_list = [
        {"id": 1, "action": "Action 1"},
        {"id": 2, "action": "Action 2"},
        {"id": 3, "action": "Action 3"},
    ]

    return action_list


@pytest.fixture
def make_notification_data():
    nofification_list = [
        {"id": 1, "notification": "Notification 1"},
        {"id": 2, "notification": "Notification 2"},
        {"id": 3, "notification": "Notification 3"},
    ]

    return nofification_list


@pytest.fixture
def order_search_pagination_mock():
    mock_ordering = MagicMock()
    mock_searching = MagicMock()
    mock_pagination = MagicMock()

    mock_ordering.field = "name"
    mock_ordering.order = "ASC"
    mock_searching.field = "name"
    mock_searching.search = "Test"
    mock_pagination.page = 1
    mock_pagination.page_size = 50

    return mock_ordering, mock_searching, mock_pagination


def generate_random_text(length=10):
    letters = string.ascii_letters + string.digits + string.punctuation + " "
    return "".join(random.choice(letters) for _ in range(length))


class TestAutomation:
    def test_create_rule(self):

        create_rule_request = schemas.CreateRuleRequest(**RULE_REQUEST)
        rules_detail = create_rule_request.to_model()

        session_mock = MagicMock()
        session_mock.add_all.return_value = None
        session_mock.bulk_save_objects.return_value = None
        session_mock.flush.return_value = None
        session_mock.commit.return_value = None

        obj = DatabaseAutomationRepository(session_mock)

        response = obj.create_rule(
            rules_detail,
        )

        session_mock.add_all.assert_called_once_with(rules_detail)
        assert session_mock.flush.call_count == 1
        assert isinstance(response, bool)

    def test_get_actions_by_rule_uuid(self, db_session):
        session, repository = db_session
        result_proxy_mock = MagicMock()
        result_proxy_mock.__iter__.return_value = iter(
            [{"name": "Change the SIM status", "value": "Deactivated"}]
        )
        session.execute.return_value = result_proxy_mock

        repository.get_actions_by_rule_uuid(rule_uuid=1)
        assert session.execute.call_count == 1

    def test_get_notification_by_rule_uuid(self, db_session):
        session, respository = db_session
        result_proxy_mock = MagicMock()
        result_proxy_mock.__iter__.return_value = iter(
            [{"name": "Send Email", "value": True}]
        )
        session.execute.return_value = result_proxy_mock
        respository.get_notification_by_rule_uuid(rule_uuid=1)
        assert session.execute.call_count == 1

    def test_get_rule_by_uuid(self, db_session):
        session, repository = db_session
        Row = namedtuple(
            "Row",
            [
                "id",
                "uuid",
                "account_id",
                "rule_type_id",
                "rule_type",
                "rule_category_id",
                "rule_category",
                "rule_definition_id",
                "rule_definition",
                "rule_name",
                "data_volume",
                "unit",
                "status",
                "lock",
            ],
        )
        data = {
            "id": 1,
            "uuid": UUID("123e4567-e89b-12d3-a456-************"),
            "account_id": 1,
            "rule_type_id": 1,
            "rule_type": "Usage Monitoring",
            "rule_category_id": 1,
            "rule_category": "Cycle To Date Data Usage",
            "rule_definition_id": 1,
            "rule_definition": "Data usage exceeds a specified limit",
            "rule_name": "Rule1",
            "data_volume": 100,
            "unit": "KB",
            "status": True,
            "lock": False,
        }

        row_object = Row(**data)
        session.execute.return_value.first.return_value = row_object

        result = repository.get_rule_by_uuid(
            UUID("123e4567-e89b-12d3-a456-************")
        )

        assert result.id == 1
        assert result.uuid == UUID("123e4567-e89b-12d3-a456-************")
        assert result.rule_type == RULE_BY_UUID_RULE_TYPE
        assert result.rule_category == RULE_BY_UUID_RULE_CATEGORY
        assert result.rule_definition == RULE_BY_UUID_RULE_DEFINITIION
        assert result.data_volume == 100
        assert result.unit == "KB"
        assert result.status is True
        assert result.lock is False

    def test_get_rule_by_uuid_rule_not_found(self, db_session):
        session, repository = db_session
        session.execute.return_value.first.return_value = None

        with pytest.raises(RuleNotFound):
            repository.get_rule_by_uuid(UUID("123e4567-e89b-12d3-a456-************"))

    def test_get_rule_with_pagination_and_ordering(self):
        mock_get_all_rule_details = MagicMock()
        func.get_all_rule_details = mock_get_all_rule_details  # type: ignore

        # Mock the SQLAlchemy response
        mock_subquery = select(
            [
                literal_column("1").label("id"),
                literal_column("'bdb5365e-40c1-4902-b8d2-55a13a72d3ea'").label("uuid"),
                literal_column("1").label("account_id"),
                literal_column("'Rule 1'").label("rule_name"),
                literal_column("400000").label("data_volume"),
                literal_column("'KB'").label("unit"),
                literal_column("true").label("status"),
                literal_column("'Usage Monitoring'").label("rule_type"),
                literal_column("'Cycle to date data usage'").label("rule_category"),
                literal_column("'Data usage exceeds a specified limit'").label(
                    "rule_definition"
                ),
            ]
        )

        mock_get_all_rule_details.return_value = mock_subquery
        expected_response = RulesData(**RULE_DETAIL_1)
        # Mock database session behavior
        mock_session = MagicMock()
        mock_execute_result = MagicMock()
        mock_execute_result.mappings.return_value = [expected_response]

        mock_session.execute.return_value = mock_execute_result

        # Mock the repository and inject the mocked session
        obj = DatabaseAutomationRepository(session=mock_session)

        # Define input arguments
        account_id = 1
        ordering = MagicMock(field="rule_name", order="ASC")
        searching = MagicMock(search="Usage")
        pagination = MagicMock(offset=0, page_size=10)

        # Call the `get_rule` method
        result = list(obj.get_rule(account_id, ordering, searching, pagination))

        mock_session.execute.assert_called_once()

        # Assertions for the result
        assert len(result) == 1
        assert result[0].id == 1
        assert result[0].uuid == "bdb5365e-40c1-4902-b8d2-55a13a72d3ea"
        assert result[0].account_id == 1
        assert result[0].data_volume == 400000
        assert result[0].unit == "KB"
        assert result[0].status is True
        assert result[0].rule_type == "Usage Monitoring"
        assert result[0].rule_category == "Cycle to date data usage"
        assert result[0].rule_definition == "Data usage exceeds a specified limit"

    def test_rule_gategory(self, make_rule_type_models):
        session_mock = MagicMock()
        result_proxy_mock = MagicMock()
        result_proxy_mock.mappings.return_value = [
            {"id": model.id, "rule_type": model.rule_type}
            for model in make_rule_type_models()
        ]
        session_mock.execute.return_value = result_proxy_mock
        obj = DatabaseAutomationRepository(session_mock)
        response = obj.rule_type()
        assert list(response) == make_rule_type_models()

    def test_rule_definition(self, make_rule_definition_models):
        rule_category_id = 1

        session_mock = MagicMock()
        result_proxy_mock = MagicMock()
        result_proxy_mock.mappings.return_value = [
            {
                "id": model.id,
                "definition": model.definition,
                "category": model.category,
            }
            for model in make_rule_definition_models()
        ]
        session_mock.execute.return_value = result_proxy_mock

        obj = DatabaseAutomationRepository(session_mock)
        response = obj.rule_definition(rule_category_id)
        assert list(response) == make_rule_definition_models()

    def test_get_actions(self, make_action_data):
        actionData = make_action_data
        session_mock = MagicMock()
        execute_mock = session_mock.execute.return_value
        mappings_mock = execute_mock.mappings.return_value
        mappings_mock.__iter__.return_value = actionData

        obj = DatabaseAutomationRepository(session_mock)
        response = list(
            obj.get_actions(
                action_id=None, pagination=None, ordering=None, searching=None
            )
        )
        assert response[0].action == actionData[0]["action"]
        assert len(response) == len(actionData)

    def test_get_actions_with_action_id(self, make_action_data):
        actionData = make_action_data
        session_mock = MagicMock()
        execute_mock = session_mock.execute.return_value
        mappings_mock = execute_mock.mappings.return_value
        mappings_mock.__iter__.return_value = actionData

        obj = DatabaseAutomationRepository(session_mock)
        response = list(
            obj.get_actions(action_id=5, pagination=None, ordering=None, searching=None)
        )
        assert response[0].action == actionData[0]["action"]
        assert len(response) == len(actionData)

    def test_get_actions_with_pagination(self, make_action_data):
        pagination = Pagination(page=1, page_size=5)

        actionData = make_action_data
        session_mock = MagicMock()
        execute_mock = session_mock.execute.return_value
        mappings_mock = execute_mock.mappings.return_value
        mappings_mock.__iter__.return_value = actionData

        obj = DatabaseAutomationRepository(session_mock)
        response = list(
            obj.get_actions(
                ordering=None,
                searching=None,
                pagination=pagination,
            )
        )
        assert response[0].action == actionData[0]["action"]
        assert len(response) == len(actionData)

    @pytest.mark.skip(reason="Not implemented")
    def test_get_actions_with_searching(self, make_action_data):
        searching = Searching(search="Test", fields={"action"})
        actionData = make_action_data
        session_mock = MagicMock()
        execute_mock = session_mock.execute.return_value
        mappings_mock = execute_mock.mappings.return_value
        mappings_mock.__iter__.return_value = actionData

        obj = DatabaseAutomationRepository(session_mock)
        response = list(obj.get_actions(searching=searching))
        assert response[0].action == actionData[0]["action"]
        assert len(response) == len(actionData)

    def test_get_actions_count(self):
        actionCount = 5
        session_mock = MagicMock()
        session_mock.execute().scalar_one.return_value = actionCount
        obj = DatabaseAutomationRepository(session_mock)
        response = obj.get_actions_count(action_id=None, searching=None)
        assert response == actionCount

    def test_count_actions_by_id(self):
        actionCount = 1
        session_mock = MagicMock()
        session_mock.execute().scalar_one.return_value = actionCount
        obj = DatabaseAutomationRepository(session_mock)
        response = obj.get_actions_count(action_id=4, searching=None)
        assert response == actionCount

    @pytest.mark.skip(reason="Not implemented")
    def test_count_actions_with_search(self):
        searching = Searching(search="Test", fields={"action"})
        actionCount = 1
        session_mock = MagicMock()
        session_mock.execute().scalar_one.return_value = actionCount
        obj = DatabaseAutomationRepository(session_mock)
        response = obj.get_actions_count(action_id=4, searching=searching)
        assert response == actionCount

    def test_get_notification(self, make_notification_data):
        notificationData = make_notification_data
        session_mock = MagicMock()
        execute_mock = session_mock.execute.return_value
        mappings_mock = execute_mock.mappings.return_value
        mappings_mock.__iter__.return_value = notificationData

        obj = DatabaseAutomationRepository(session_mock)
        response = list(
            obj.get_notification_list(
                notification_id=None, pagination=None, ordering=None, searching=None
            )
        )
        assert response[0].notification == notificationData[0]["notification"]
        assert len(response) == 3

    @pytest.mark.skip(reason="Not implemented")
    def test_get_notification_with_notification_id(self, make_notification_data):
        notificationData = make_notification_data
        session_mock = MagicMock()
        execute_mock = session_mock.execute.return_value
        mappings_mock = execute_mock.mappings.return_value
        mappings_mock.__iter__.return_value = notificationData

        obj = DatabaseAutomationRepository(session_mock)
        response = list(
            obj.get_notification_list(
                notification_id=1, pagination=None, ordering=None, searching=None
            )
        )
        assert response[0].notification == notificationData[0]["notification"]
        assert len(response) == 1

    def test_get_notification_with_pagination(self, make_notification_data):
        pagination = Pagination(page=1, page_size=5)
        notificationData = make_notification_data
        session_mock = MagicMock()
        execute_mock = session_mock.execute.return_value
        mappings_mock = execute_mock.mappings.return_value
        mappings_mock.__iter__.return_value = notificationData

        obj = DatabaseAutomationRepository(session_mock)
        response = list(
            obj.get_notification_list(
                notification_id=None,
                pagination=pagination,
                ordering=None,
                searching=None,
            )
        )
        assert response[0].notification == notificationData[0]["notification"]
        assert len(response) == 3

    @pytest.mark.skip(reason="Not implemented")
    def test_get_notification_with_searching(self):
        pagination = Pagination(page=1, page_size=5)
        searching = Searching(search="Test", fields={"notification"})
        notificationData = make_notification_data
        session_mock = MagicMock()
        execute_mock = session_mock.execute.return_value
        mappings_mock = execute_mock.mappings.return_value
        mappings_mock.__iter__.return_value = notificationData

        obj = DatabaseAutomationRepository(session_mock)
        response = list(
            obj.get_notification_list(
                notification_id=None,
                pagination=pagination,
                ordering=None,
                searching=searching,
            )
        )
        assert response[0].notification == notificationData[0]["notification"]
        assert len(response) == 3

    def test_get_notification_count(self):
        notificationCount = 5
        session_mock = MagicMock()

        session_mock.execute().scalar_one.return_value = notificationCount

        obj = DatabaseAutomationRepository(session_mock)
        response = obj.get_notification_count(notification_id=None, searching=None)
        assert response == notificationCount

    def test_count_notification_by_id(self):
        notificationCount = 1
        session_mock = MagicMock()

        session_mock.execute().scalar_one.return_value = notificationCount

        obj = DatabaseAutomationRepository(session_mock)
        response = obj.get_notification_count(notification_id=5, searching=None)
        assert response == notificationCount

    @pytest.mark.skip(reason="Not implemented")
    def test_count_notification_with_search(self):
        notificationCount = 1
        session_mock = MagicMock()
        searching = Searching(search="Test", fields={"notification"})

        session_mock.execute().scalar_one.return_value = notificationCount

        obj = DatabaseAutomationRepository(session_mock)
        response = obj.get_notification_count(notification_id=5, searching=searching)
        assert response == notificationCount

    def test_notification_value_details(self):
        notification_values = [
            MagicMock(spec=model.NotificationValue),
            MagicMock(spec=model.NotificationValue),
        ]
        rules_notification_id = 123

        session_mock = MagicMock()
        repo = DatabaseAutomationRepository(session=session_mock)

        result = repo._notification_value_details(
            notification_value=notification_values,
            rules_notification_id=rules_notification_id,
        )

        assert result == notification_values
        for value in notification_values:
            assert value.rules_notification_id == rules_notification_id

    def test_create_rules_notification_success(self):
        notification_values = [
            MagicMock(spec=model.NotificationValue),
            MagicMock(spec=model.NotificationValue),
        ]
        rules_notification_detail = MagicMock(spec=model.RulesNotification)
        rules_notification_detail.notification = True
        rules_notification_detail.notification_value = notification_values
        rules_notification_detail.id = 123

        session_mock = MagicMock()
        repo = DatabaseAutomationRepository(session=session_mock)

        result = repo.create_rules_notification(rules_notification_detail)

        assert result is True
        session_mock.add.assert_called_once_with(rules_notification_detail)
        session_mock.flush.assert_called_once()
        session_mock.bulk_save_objects.assert_called_once_with(notification_values)
        session_mock.commit.assert_called_once()

    def test_create_rules_notification_without_notification_value(self):
        rules_notification_detail = MagicMock(spec=model.RulesNotification)
        rules_notification_detail.notification = True
        rules_notification_detail.notification_value = None
        rules_notification_detail.id = 123

        session_mock = MagicMock()
        repo = DatabaseAutomationRepository(session=session_mock)

        result = repo.create_rules_notification(rules_notification_detail)

        assert result is True
        session_mock.add.assert_called_once_with(rules_notification_detail)
        session_mock.flush.assert_called_once()
        session_mock.bulk_save_objects.assert_not_called()
        session_mock.commit.assert_called_once()

    def test_create_rules_notification_without_notification(self):
        rules_notification_detail = MagicMock(spec=model.RulesNotification)
        rules_notification_detail.notification = None
        rules_notification_detail.notification_value = None
        rules_notification_detail.id = None

        session_mock = MagicMock()
        repo = DatabaseAutomationRepository(session=session_mock)

        result = repo.create_rules_notification(rules_notification_detail)

        assert result is True
        session_mock.add.assert_not_called()
        session_mock.flush.assert_not_called()
        session_mock.bulk_save_objects.assert_not_called()
        session_mock.commit.assert_called_once()

    def test_rule_category_with_all_filters(self):
        orm_mock = MagicMock()
        orm_mock.rule_category.c.rule_type_id = MagicMock()
        orm_mock.rule_category.c.id = MagicMock()
        orm_mock.rule_category.c.category = MagicMock()
        orm_mock.rule_type.c.rule_type = MagicMock()

        session_mock = MagicMock()
        execute_result_mock = MagicMock()
        execute_result_mock.mappings.return_value = [
            {
                "rule_type_id": 1,
                "id": 101,
                "category": "Test Category",
                "rule_type": "Usage Monitoring",
                "rule_category_code": "TC",
            }
        ]
        session_mock.execute.return_value = execute_result_mock

        pagination = Pagination(page=1, page_size=10)
        ordering = Ordering(field="category", order="DESC")

        repo = DatabaseAutomationRepository(session=session_mock)
        repo.orm = orm_mock

        result = list(
            repo.rule_category(
                type_id=1,
                pagination=pagination,
                ordering=ordering,
            )
        )

        session_mock.execute.assert_called_once()
        assert len(result) == 1
        assert result[0].rule_type_id == 1
        assert result[0].id == 101
        assert result[0].category == "Test Category"
        assert result[0].rule_type == "Usage Monitoring"

    def test_rule_category_without_filters(self):
        orm_mock = MagicMock()
        orm_mock.rule_category.c.rule_type_id = MagicMock()
        orm_mock.rule_category.c.id = MagicMock()
        orm_mock.rule_category.c.category = MagicMock()
        orm_mock.rule_type.c.rule_type = MagicMock()

        session_mock = MagicMock()
        execute_result_mock = MagicMock()
        execute_result_mock.mappings.return_value = [
            {
                "rule_type_id": 1,
                "id": 101,
                "category": "Test Category",
                "rule_type": "Usage Monitoring",
                "rule_category_code": "TC",
            }
        ]
        session_mock.execute.return_value = execute_result_mock

        repo = DatabaseAutomationRepository(session=session_mock)
        repo.orm = orm_mock

        result = list(repo.rule_category())

        session_mock.execute.assert_called_once()
        assert len(result) == 1
        assert result[0].rule_type_id == 1
        assert result[0].id == 101
        assert result[0].category == "Test Category"
        assert result[0].rule_type == "Usage Monitoring"

    def test_rule_category_with_pagination_and_ordering(self):
        orm_mock = MagicMock()
        orm_mock.rule_category.c.rule_type_id = MagicMock()
        orm_mock.rule_category.c.id = MagicMock()
        orm_mock.rule_category.c.category = MagicMock()
        orm_mock.rule_type.c.rule_type = MagicMock()

        session_mock = MagicMock()
        execute_result_mock = MagicMock()
        execute_result_mock.mappings.return_value = [
            {
                "rule_type_id": 2,
                "id": 102,
                "category": "Another Category",
                "rule_type": "Data Limit Monitoring",
                "rule_category_code": "ANCA",
            }
        ]
        session_mock.execute.return_value = execute_result_mock

        pagination = Pagination(page=1, page_size=5)
        ordering = Ordering(field="id", order="DESC")

        repo = DatabaseAutomationRepository(session=session_mock)
        repo.orm = orm_mock

        result = list(
            repo.rule_category(
                pagination=pagination,
                ordering=ordering,
            )
        )

        session_mock.execute.assert_called_once()
        assert len(result) == 1
        assert result[0].rule_type_id == 2
        assert result[0].id == 102
        assert result[0].category == "Another Category"
        assert result[0].rule_type == "Data Limit Monitoring"

    def test_rule_definition_count_with_searching(self):
        orm_mock = MagicMock()
        orm_mock.rule_definition.c.id = MagicMock()
        orm_mock.rule_definition.c.rule_category_id = MagicMock()

        session_mock = MagicMock()
        execute_result_mock = MagicMock()
        execute_result_mock.scalar_one.return_value = 5  # Mock count result
        session_mock.execute.return_value = execute_result_mock

        repo = DatabaseAutomationRepository(session=session_mock)
        repo.orm = orm_mock

        result = repo.rule_definition_count(rule_category_id=1)

        session_mock.execute.assert_called_once()
        assert result == 5

    def test_rule_definition_count_without_searching(self):
        orm_mock = MagicMock()
        orm_mock.rule_definition.c.id = MagicMock()
        orm_mock.rule_definition.c.rule_category_id = MagicMock()

        session_mock = MagicMock()
        execute_result_mock = MagicMock()
        execute_result_mock.scalar_one.return_value = 10
        session_mock.execute.return_value = execute_result_mock

        repo = DatabaseAutomationRepository(session=session_mock)
        repo.orm = orm_mock

        result = repo.rule_definition_count(rule_category_id=2)

        session_mock.execute.assert_called_once()
        assert result == 10

    def test_reset_rules_action_and_notification(self):
        session_mock = MagicMock()

        repo = DatabaseAutomationRepository(session=session_mock)
        repo.get_rule_by_uuid = MagicMock()

        sample_uuid = uuid4()

        session_mock.execute = MagicMock()

        session_mock.execute.return_value.mappings.return_value = [{"id": 1}]

        repo.reset_rules_action_and_notification(rules_uuid=sample_uuid)

        repo.get_rule_by_uuid.assert_called_once_with(rule_uuid=sample_uuid)
        session_mock.begin_nested.assert_called_once()
        assert session_mock.execute.call_count == 4

    def test_update_rule(self):
        session_mock = MagicMock()
        repo = DatabaseAutomationRepository(session=session_mock)

        mock_rule = model.Rules(
            uuid=uuid4(),
            account_id=1,
            rule_type_id=1,
            rule_category_id=2,
            rule_definition_id=3,
            rule_name="Test Rule",
            data_volume=100,
            status=True,
            unit="MB",
            created_by="Test User",
            ip_address="127.0.0.1",
            lock=False,
            notifications=[],
            actions=[],
        )

        session_mock.query.return_value.filter.return_value.update.return_value = 1

        result = repo.update_rule(rules_detail=mock_rule)

        session_mock.query.assert_called_once_with(orm.rules)
        session_mock.query.return_value.filter.return_value.update.assert_called_once_with(  # noqa
            {
                orm.rules.c.rule_type_id: mock_rule.rule_type_id,
                orm.rules.c.rule_category_id: mock_rule.rule_category_id,
                orm.rules.c.rule_definition_id: mock_rule.rule_definition_id,
                orm.rules.c.rule_name: mock_rule.rule_name,
                orm.rules.c.data_volume: mock_rule.data_volume,
                orm.rules.c.status: mock_rule.status,
                orm.rules.c.unit: mock_rule.unit,
                orm.rules.c.lock: mock_rule.lock,
            }
        )
        assert result is True

    def test_update_rule_integrity_error_rule_already_exists(self):
        session_mock = MagicMock()
        repo = DatabaseAutomationRepository(session=session_mock)

        mock_rule = model.Rules(
            uuid=uuid4(),
            account_id=1,
            rule_type_id=1,
            rule_category_id=2,
            rule_definition_id=3,
            rule_name="Test Rule",
            data_volume=100,
            status=True,
            unit="MB",
            created_by="Test User",
            ip_address="127.0.0.1",
            lock=False,
            notifications=[],
            actions=[],
        )

        session_mock.query.return_value.filter.return_value.update.side_effect = IntegrityError(  # noqa
            "IntegrityError: (sqlite3.IntegrityError) UNIQUE constraint failed: rules.rule_name (details that already exists.)",  # noqa
            params=None,
            orig=None,
        )

        try:
            repo.update_rule(rules_detail=mock_rule)
        except RuleAlreadyExist:
            pass
        else:
            assert False, "RuleAlreadyExist exception not raised."

    def test_update_rule_integrity_error_create_rule_error(self):
        session_mock = MagicMock()
        repo = DatabaseAutomationRepository(session=session_mock)

        mock_rule = model.Rules(
            uuid=uuid4(),
            account_id=1,
            rule_type_id=1,
            rule_category_id=2,
            rule_definition_id=3,
            rule_name="Test Rule",
            data_volume=100,
            status=True,
            unit="MB",
            created_by="Test User",
            ip_address="127.0.0.1",
            lock=False,
            notifications=[],
            actions=[],
        )

        session_mock.query.return_value.filter.return_value.update.side_effect = (
            IntegrityError("Some other integrity error", params=None, orig=None)
        )

        try:
            repo.update_rule(rules_detail=mock_rule)
        except CreateRuleError:
            pass
        else:
            assert False, "CreateRuleError exception not raised."

    def test_update_rule_status_by_rule_uuid(self):
        session_mock = MagicMock()
        repo = DatabaseAutomationRepository(session=session_mock)

        mock_uuid = uuid4()
        mock_status = True

        session_mock.query.return_value.filter.return_value.update.return_value = 1

        repo.update_rule_status_by_rule_uuid(
            rule_uuid=mock_uuid, rule_status=mock_status
        )

        session_mock.query.assert_called_once_with(orm.rules)
        session_mock.query.return_value.filter.return_value.update.assert_called_once_with(  # noqa
            {orm.rules.c.status: mock_status}
        )
        session_mock.commit.assert_called_once()

    def test_update_rule_status_by_rule_uuid_no_update(self):
        session_mock = MagicMock()
        repo = DatabaseAutomationRepository(session=session_mock)

        mock_uuid = uuid4()
        mock_status = False

        session_mock.query.return_value.filter.return_value.update.return_value = 0

        repo.update_rule_status_by_rule_uuid(
            rule_uuid=mock_uuid, rule_status=mock_status
        )

        session_mock.query.assert_called_once_with(orm.rules)
        session_mock.query.return_value.filter.return_value.update.assert_called_once_with(  # noqa
            {orm.rules.c.status: mock_status}
        )
        session_mock.commit.assert_called_once()

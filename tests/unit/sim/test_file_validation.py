import unittest
from unittest.mock import MagicMock, patch

import pytest

from sim.exceptions import FileSizeExceededLimit
from sim.parser import SIMCardCSVParser


class TestFileValidation(unittest.TestCase):
    @patch("sim.parser.settings.MAX_FILE_SIZE_MB", 5)
    def test_validate_file_size_under_limit(self):
        # Create a mock file object
        mock_file = MagicMock()
        mock_file.fileno.return_value = 1

        # Create a SIMCardCSVParser instance
        parser = SIMCardCSVParser.__new__(SIMCardCSVParser)

        # Mock os.fstat to return a file size of 3MB
        with patch("sim.parser.os.fstat") as mock_fstat:
            mock_fstat.return_value.st_size = 3 * 1024 * 1024  # 3MB

            # This should not raise an exception
            parser.validate_file_size(file=mock_file)

    @patch("sim.parser.settings.MAX_FILE_SIZE_MB", 5)
    def test_validate_file_size_over_limit(self):
        # Create a mock file object
        mock_file = MagicMock()
        mock_file.fileno.return_value = 1

        # Create a SIMCardCSVParser instance
        parser = SIMCardCSVParser.__new__(SIMCardCSVParser)

        # Mock os.fstat to return a file size of 6MB
        with patch("sim.parser.os.fstat") as mock_fstat:
            mock_fstat.return_value.st_size = 6 * 1024 * 1024  # 6MB

            # This should raise FileSizeExceededLimit
            with pytest.raises(FileSizeExceededLimit):
                parser.validate_file_size(file=mock_file)

    @patch("sim.parser.settings.MAX_FILE_SIZE_MB", 5)
    def test_validate_file_size_mock_object(self):
        # Create a mock file that will raise AttributeError when fileno is called
        mock_file = MagicMock()
        mock_file.fileno.side_effect = AttributeError("Mock has no fileno")

        # Create a SIMCardCSVParser instance
        parser = SIMCardCSVParser.__new__(SIMCardCSVParser)

        # This should not raise an exception (should be caught by the try/except)
        parser.validate_file_size(file=mock_file)

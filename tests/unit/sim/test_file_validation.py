import unittest
from unittest.mock import MagicMock, patch

import pytest

from sim.exceptions import FileSizeExceededLimit
from sim.services import SimService


class TestFileValidation(unittest.TestCase):
    @patch("sim.services.settings.MAX_FILE_SIZE_MB", 5)
    def test_validate_file_size_under_limit(self):
        # Create a mock file object
        mock_file = MagicMock()
        mock_file.fileno.return_value = 1

        # Create a SimService instance
        sim_service = MagicMock(spec=SimService)

        # Mock os.fstat to return a file size of 3MB
        with patch("os.fstat") as mock_fstat:
            mock_fstat.return_value.st_size = 3 * 1024 * 1024  # 3MB

            # This should not raise an exception
            SimService.validate_file_size(sim_service, file=mock_file)

    @patch("sim.services.settings.MAX_FILE_SIZE_MB", 5)
    def test_validate_file_size_over_limit(self):
        # Create a mock file object
        mock_file = MagicMock()
        mock_file.fileno.return_value = 1

        # Create a SimService instance
        sim_service = MagicMock(spec=SimService)

        # Mock os.fstat to return a file size of 6MB
        with patch("os.fstat") as mock_fstat:
            mock_fstat.return_value.st_size = 6 * 1024 * 1024  # 6MB

            # This should raise FileSizeExceededLimit
            with pytest.raises(FileSizeExceededLimit):
                SimService.validate_file_size(sim_service, file=mock_file)

    @patch("sim.services.settings.MAX_FILE_SIZE_MB", 5)
    def test_validate_file_size_mock_object(self):
        # Create a mock file that will raise AttributeError when fileno is called
        mock_file = MagicMock()
        mock_file.fileno.side_effect = AttributeError("Mock has no fileno")

        # Create a SimService instance
        sim_service = MagicMock(spec=SimService)

        # This should not raise an exception (should be caught by the try/except)
        SimService.validate_file_size(sim_service, file=mock_file)

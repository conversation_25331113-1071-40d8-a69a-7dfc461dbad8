import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch
from uuid import UUID

from api.cdrdata.examples import (
    CDR_DATA_XML,
    CDR_SMS_XML,
    CDR_SMS_XML_INVALID,
    CDR_VOICE_XML,
)
from cdrdata.adapters.repository import AbstractCdrRepository, InMemoryCdrRepository
from cdrdata.domain import model
from cdrdata.domain.ports import AbstractMarketShare
from cdrdata.services import AbstractCdrService, CdrService
from common.types import ICCID, IMSI, Month, SimProfile, SimStatus
from sim.adapters.externalapi import FakeMarketShareAPI
from sim.adapters.repository import InMemorySimRepository
from sim.domain.model import MSISDNFactor

import pytest  # isort: skip


CDR_DATA_ROOT = ET.fromstring(CDR_DATA_XML)
CDR_VOICE_ROOT = ET.fromstring(CDR_VOICE_XML)
CDR_SMS_ROOT = ET.fromstring(CDR_SMS_XML)
INVALID_XML_STRING = """<invalid></invalid>"""
CDR_DATA = model.CDR(
    uuid=UUID("123e4567-e89b-12d3-a456-************"),
    imsi=IMSI("234588560667860"),
    iccid=ICCID("8944538531005850000"),
    country="GBR",
    country_name="United Kingdom",
    carrier="GBRME",
    data=None,
    voice=None,
    sms=None,
    data_usage=None,
    voice_usage=None,
    cdr_object_id=None,
)


class CdrdataContract:
    @pytest.fixture
    def cdrdata_repository(self, *args, **kwargs) -> AbstractCdrRepository:
        raise NotImplementedError()

    @pytest.fixture
    def cdr_repository(self) -> AbstractCdrRepository:
        return InMemoryCdrRepository()

    @pytest.fixture
    def market_share(self) -> AbstractMarketShare:
        return FakeMarketShareAPI()  # type: ignore

    @pytest.fixture
    def cdr_service(self, cdr_repository, market_share) -> AbstractCdrService:
        return CdrService(cdr_repository=cdr_repository, market_share=market_share)

    @pytest.fixture
    def usage_records_fixture(self):
        usage_data = []

        for i in range(1, 5):
            cdr_uuid = f"uuid{i}"
            imsi = f"imsi{i}"
            iccid_value = "12345" if i % 2 != 0 else "67890"
            iccid = ICCID(iccid_value)
            month = Month(2023, 5 if i < 3 else 4, 1)
            usage = i * 10

            entry = {
                "cdr_uuid": cdr_uuid,
                "imsi": imsi,
                "iccid": iccid,
                "month": month,
                "usage": usage,
            }

            usage_data.append(entry)
        return usage_data

    @pytest.fixture
    def make_cdr_last_session(self):
        return model.lastSessionCDR(
            iccid="**********987687654",
            imsi="09876543123476",
            last_session=datetime.today(),
        )

    def test_usage_monthly_total(self, usage_records_fixture):
        repository = InMemoryCdrRepository()
        repository.add_usage_data(usage_records_fixture)
        expected_total = 30
        assert (
            repository.usage_monthly_total(iccid_list=[ICCID("12345"), ICCID("67890")])
            == expected_total
        )

    def test_get_sim_usage_by_imsi(self, usage_records_fixture):
        repository = InMemoryCdrRepository()
        repository.add_usage_data(usage_records_fixture)
        expected_usage = [
            {
                "cdr_uuid": "uuid1",
                "imsi": "imsi1",
                "iccid": ICCID("12345"),
                "month": Month(2023, 5, 1),
                "usage": 10,
            },
            {
                "cdr_uuid": "uuid2",
                "imsi": "imsi2",
                "iccid": ICCID("67890"),
                "month": Month(2023, 5, 1),
                "usage": 20,
            },
        ]
        assert (
            repository.get_sim_usage_by_imsi(
                imsi=["imsi1", "imsi2"],
                month=datetime.fromisoformat("2023-05-04T18:47:05"),
            )
            == expected_usage
        )

    @pytest.fixture
    def sims_usage_export(self):
        sim_data = []
        for i in range(0, 3):
            data = {
                "sim_id": 1 + 1,
                "account_id": i + 1,
                "iccid": str(***************6789 + i),
                "msisdn": str(********** + i),
                "imsi": str(*************** + i),
                "type": "NANO" if i % 2 == 0 else "MICRO",
                "allocation_reference": f"reference{i+1}",
                "allocation_date": datetime.now(),
                "sim_status": SimStatus.ACTIVE if i % 2 == 0 else SimStatus.DEACTIVATED,
                "usage": 100 if i % 2 == 0 else 50,
                "rate_plan": "PAYG",
                "ee_usage": 100 + i,
                "sim_profile": SimProfile.DATA_ONLY,
                "msisdn_factor": MSISDNFactor.INTERNATIONAL,
            }
            sim_data.append(data)
        return sim_data

    @pytest.fixture
    def sim_cdr_history_export_fixture(self):
        data = {
            "iccid": "8944538532046590001",
            "imsi": "***************",
            "country": "USA",
            "carrier": "GBRME",
            "sessionStarttime": "2023-03-11 21:13:44",
            "sessionEndtime": "2023-03-11 21:13:44",
            "duration": 40,
            "dataVolume": 9431,
            "countryName": "United States",
            "carrierName": "EE",
        }
        return data

    @pytest.fixture
    def sim_cdr_history_fixture(self):
        data = {
            "iccid": "8944538531005850000",
            "imsi": "234588560667860",
            "country": "GBR",
            "carrier": "GBRME",
            "sessionStarttime": "2023-04-05T18:47:05",
            "sessionEndtime": "2023-04-05 18:47:05",
            "duration": 40,
            "dataVolume": 9431,
            "countryName": "United Kingdom",
            "carrierName": "EE",
        }
        return data

    @pytest.fixture
    def sim_cdr_voice_fixture(self):
        data = {
            "iccid": "8944538531005850000",
            "imsi": "234588560667860",
            "country": "GBR",
            "carrier": "GBRME",
            "callDate": "2023-04-05T18:47:05",
            "callNumber": 123456789,
            "callMinutes": 40,
            "countryName": "United Kingdom",
            "carrierName": "EE",
        }
        return data

    @pytest.fixture
    def sim_cdr_voice_export_fixture(self):
        data = {
            "iccid": "8944538532046590001",
            "imsi": "***************",
            "country": "USA",
            "carrier": "GBRME",
            "callDate": "2023-03-10 08:39:47",
            "callNumber": 447452380001,
            "callMinutes": 85,
            "countryName": "United States",
            "carrierName": "EE",
        }
        return data

    @pytest.fixture
    def sim_cdr_sms_fixture(self):
        data = {
            "iccid": "8944538531005850000",
            "imsi": "234588560667860",
            "country": "GBR",
            "carrier": "GBRME",
            "DateSent": "2023-04-05T18:47:05",
            "SentFrom": 123456789,
            "SentTo": 123456788,
            "countryName": "United Kingdom",
            "carrierName": "EE",
        }
        return data

    @pytest.fixture
    def sim_cdr_sms_export_fixture(self):
        data = {
            "iccid": "8944538532046590001",
            "imsi": "***************",
            "country": "USA",
            "carrier": "GBRME",
            "DateSent": "2023-03-26 08:43:33",
            "SentFrom": 447452380001,
            "SentTo": 447452380002,
            "countryName": "United States",
            "carrierName": "EE",
        }
        return data

    def test_get_sim_usage(self, sims_usage_export):
        repository = InMemorySimRepository()
        repository.add_sim_card_data(sims_usage_export)
        assert repository.get_sim_usage(1, None) is not None
        for i in repository.get_sim_usage(1, None):
            assert i.iccid == "***************6789"
            assert i.imsi == "***************"
            assert i.msisdn == "**********"
            assert i.sim_status == SimStatus.ACTIVE
            assert i.usage == 100

    def test_connection_history(self, sim_cdr_history_fixture):
        repository = InMemoryCdrRepository()
        repository.add_connection_history(sim_cdr_history_fixture)
        imsi = "234588560667860"
        result = list(repository.connection_history(imsi, "2023-04-05 18:47:05"))
        expected_result = [
            model.SimCDRHistory(
                iccid="8944538531005850000",
                imsi="234588560667860",
                country="GBR",
                carrier="GBRME",
                session_starttime="2023-04-05T18:47:05",
                session_endtime="2023-04-05 18:47:05",
                duration=40,
                data_volume=9431,
                country_name="United Kingdom",
                carrier_name="EE",
            )
        ]
        assert result == expected_result

    def test_connection_history_export(self, sim_cdr_history_export_fixture):
        repository = InMemoryCdrRepository()
        repository.add_connection_history(sim_cdr_history_export_fixture)
        imsi = "***************"
        result = list(repository.connection_history(imsi, "2023-03-11 21:13:44"))
        expected_result = [
            model.SimCDRHistory(
                iccid="8944538532046590001",
                imsi="***************",
                country="USA",
                carrier="GBRME",
                session_starttime="2023-03-11 21:13:44",
                session_endtime="2023-03-11 21:13:44",
                duration=40,
                data_volume=9431,
                country_name="United States",
                carrier_name="EE",
            )
        ]
        assert result == expected_result

    def test_voice_connection_history(self, sim_cdr_voice_fixture):
        repository = InMemoryCdrRepository()
        repository.add_voice_connection_history(sim_cdr_voice_fixture)
        imsi = "234588560667860"
        result = list(repository.voice_connection_history(imsi, "2023-04-05 18:47:05"))
        expected_result = [
            model.SimVoiceCDRHistory(
                iccid="8944538531005850000",
                imsi="234588560667860",
                country="GBR",
                carrier="GBRME",
                call_date="2023-04-05T18:47:05",
                call_number=123456789,
                call_minutes=40,
                country_name="United Kingdom",
                carrier_name="EE",
            )
        ]
        assert result == expected_result

    def test_voice_connection_history_export(self, sim_cdr_voice_export_fixture):
        repository = InMemoryCdrRepository()
        repository.add_voice_connection_history(sim_cdr_voice_export_fixture)
        imsi = "***************"
        result = list(repository.voice_connection_history(imsi, "2023-03-26 08:43:33"))
        expected_result = [
            model.SimVoiceCDRHistory(
                imsi="***************",
                iccid="8944538532046590001",
                country="USA",
                carrier="GBRME",
                call_date="2023-03-10 08:39:47",
                call_number="447452380001",
                call_minutes="85",
                country_name="United States",
                carrier_name="EE",
            )
        ]
        assert result == expected_result

    def test_sms_connection_history(self, sim_cdr_sms_fixture):
        repository = InMemoryCdrRepository()
        repository.add_sms_connection_history(sim_cdr_sms_fixture)
        imsi = "234588560667860"
        result = list(repository.sms_connection_history(imsi, "2023-04-05 18:47:05"))
        expected_result = [
            model.SimSMSCDRHistory(
                iccid="8944538531005850000",
                imsi="234588560667860",
                country="GBR",
                carrier="GBRME",
                date_sent="2023-04-05T18:47:05",
                sent_from=123456789,
                sent_to=123456788,
                country_name="United Kingdom",
                carrier_name="EE",
            )
        ]
        assert result == expected_result

    def test_sms_connection_history_export(self, sim_cdr_sms_export_fixture):
        repository = InMemoryCdrRepository()
        repository.add_sms_connection_history(sim_cdr_sms_export_fixture)
        imsi = "***************"
        result = list(repository.sms_connection_history(imsi, "2023-03-26 08:43:33"))
        expected_result = [
            model.SimSMSCDRHistory(
                iccid="8944538532046590001",
                imsi="***************",
                country="USA",
                carrier="GBRME",
                date_sent="2023-03-26 08:43:33",
                sent_from=447452380001,
                sent_to=447452380002,
                country_name="United States",
                carrier_name="EE",
            )
        ]
        assert result == expected_result

    def test_add_cdr_last_session(self, make_cdr_last_session):
        repository = InMemoryCdrRepository()
        result = repository.add_cdr_last_session(make_cdr_last_session)
        assert repository.add_cdr_last_session(result) == result

    def test_validate_last_session(self, make_cdr_last_session):
        repository = InMemoryCdrRepository()
        result = repository.add_cdr_last_session(make_cdr_last_session)
        assert repository.validate_last_session(result.iccid, result.imsi) is True

    def test_invalid_last_session(self, make_cdr_last_session):
        repository = InMemoryCdrRepository()
        result = repository.add_cdr_last_session(make_cdr_last_session)
        iccid = "***************67823"
        assert repository.validate_last_session(iccid, result.imsi) is False

    def test_update_last_session(self, make_cdr_last_session):
        repository = InMemoryCdrRepository()
        result = repository.add_cdr_last_session(make_cdr_last_session)
        result.last_session = datetime.today() + timedelta(days=1)
        assert repository.update_last_session(result) is None

    def test_get_last_session(self, make_cdr_last_session):
        repository = InMemoryCdrRepository()
        result = repository.add_cdr_last_session(make_cdr_last_session)
        last_session = repository.get_cdr_last_session(result.imsi)
        assert last_session == result.last_session

    def test_create_cdr_data(self, cdr_service):
        cdr_data = model.CDR(
            uuid=UUID("123e4567-e89b-12d3-a456-************"),
            imsi="234588560667860",
            iccid="8944538531005850000",
            country="GBR",
            country_name="United Kingdom",
            carrier="GBRME",
            data=model.CDRData(
                cdr_uuid="123e4567-e89b-12d3-a456-************",
                session_starttime="2023-04-05T18:47:05",
                session_endtime="2023-04-05 18:47:05",
                duration=40,
                data_volume=960,
            ),
            carrier_name="EE",
            voice=None,
            sms=None,
            data_usage=None,
            voice_usage=None,
            cdr_object_id=None,
        )
        result = cdr_service.create_cdr_data(cdr_data)
        assert isinstance(result, model.CDRData)

    def test_create_cdr_data_no_data(self, cdr_service):

        result = cdr_service.create_cdr_data(CDR_DATA)
        assert result is None

    def test_create_cdr_voice(self, cdr_service):
        cdr_data = model.CDR(
            uuid=UUID("123e4567-e89b-12d3-a456-************"),
            imsi="234588560667860",
            iccid="8944538531005850000",
            country="GBR",
            country_name="United Kingdom",
            carrier="GBRME",
            data=None,
            carrier_name="EE",
            voice=model.CDRVoice(
                cdr_uuid="123e4567-e89b-12d3-a456-************",
                call_date="2023-04-05T18:47:05",
                call_minutes=40,
                call_number="123456789",
            ),
            sms=None,
            data_usage=None,
            voice_usage=None,
            cdr_object_id=None,
        )
        result = cdr_service.create_cdr_voice(cdr_data)
        assert isinstance(result, model.CDRVoice)

    def test_create_cdr_data_no_voice(self, cdr_service):

        result = cdr_service.create_cdr_voice(CDR_DATA)
        assert result is None

    def test_create_cdr_sms(self, cdr_service):
        cdr_data = model.CDR(
            uuid=UUID("123e4567-e89b-12d3-a456-************"),
            imsi="234588560667860",
            iccid="8944538531005850000",
            country="GBR",
            country_name="United Kingdom",
            carrier="GBRME",
            data=None,
            carrier_name="EE",
            voice=None,
            sms=model.CDRSMS(
                cdr_uuid="123e4567-e89b-12d3-a456-************",
                date_sent="2023-04-05T18:47:05",
                sent_from="123456789",
                sent_to="123456789",
            ),
            data_usage=None,
            voice_usage=None,
            cdr_object_id=None,
        )
        result = cdr_service.create_cdr_sms(cdr_data)
        assert isinstance(result, model.CDRSMS)

    def test_create_cdr_data_no_sms(self, cdr_service):

        result = cdr_service.create_cdr_sms(CDR_DATA)
        assert result is None

    def test_create_data_usage_aggregate(self, cdr_service):
        cdr_data = model.CDR(
            uuid=UUID("123e4567-e89b-12d3-a456-************"),
            imsi="234588560667860",
            iccid="8944538531005850000",
            country="GBR",
            country_name="United Kingdom",
            carrier="GBRME",
            data=model.CDRData(
                cdr_uuid="123e4567-e89b-12d3-a456-************",
                session_starttime="2023-04-05T18:47:05",
                session_endtime="2023-04-05 18:47:05",
                duration=40,
                data_volume=960,
            ),
            carrier_name="EE",
            voice=None,
            sms=None,
            data_usage=None,
            voice_usage=None,
            cdr_object_id=None,
        )
        result = cdr_service.create_data_usage_aggregate(cdr_data)
        assert isinstance(result, model.DataUsageAggregate)

    def test_create_data_usage_aggregate_no_data(self, cdr_service):

        result = cdr_service.create_data_usage_aggregate(CDR_DATA)
        assert result is None

    def test_create_voice_aggregate(self, cdr_service):
        cdr_data = model.CDR(
            uuid=UUID("123e4567-e89b-12d3-a456-************"),
            imsi="234588560667860",
            iccid="8944538531005850000",
            country="GBR",
            country_name="United Kingdom",
            carrier="GBRME",
            data=None,
            carrier_name="EE",
            voice=model.CDRVoice(
                cdr_uuid="123e4567-e89b-12d3-a456-************",
                call_date="2023-04-05T18:47:05",
                call_minutes=40,
                call_number="123456789",
            ),
            sms=None,
            data_usage=None,
            voice_usage=None,
            cdr_object_id=None,
        )
        result = cdr_service.create_voice_aggregate(cdr_data)
        assert isinstance(result, model.VoiceUsageAggregate)

    def test_create_voice_aggregate_no_data(self, cdr_service):

        result = cdr_service.create_voice_aggregate(CDR_DATA)
        assert result is None

    def test_create_cdr(self, cdr_service):
        cdr = model.CDR(
            uuid=UUID("123e4567-e89b-12d3-a456-************"),
            imsi="234588560667860",
            iccid="8944538531005850000",
            country="GBR",
            country_name="United Kingdom",
            carrier="GBRME",
            data=None,
            carrier_name="EE",
            voice=None,
            sms=None,
            data_usage=None,
            voice_usage=None,
            cdr_object_id=None,
        )
        result = cdr_service.create_cdr(
            cdr,
            cdr_data=None,
            cdr_voice=None,
            cdr_sms=None,
            data_usage_aggregate=None,
            voice_usage_aggregate=None,
        )
        assert isinstance(result, model.CDR)

    def test_parse_xml_string(self, cdr_service):
        xml_string = CDR_DATA_XML
        result = cdr_service.parse_xml_string(xml_string)
        assert isinstance(result, ET.Element)

    def test_extract_imsi_successful(self, cdr_service):
        result = cdr_service.extract_imsi(CDR_DATA_ROOT)
        assert result == "234588570010027"

    def test_extract_imsi_not_found(self, cdr_service):
        CDR_DATA_ROOT.find("Subscriber").text = None
        with pytest.raises(ValueError) as e:
            cdr_service.extract_imsi(CDR_DATA_ROOT)
        assert str(e.value) == "IMSI not found."

    def test_extract_imsi_subscriber_not_found(self, cdr_service):
        subscriber = CDR_DATA_ROOT.find("Subscriber")
        CDR_DATA_ROOT.remove(subscriber)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_imsi(CDR_DATA_ROOT)
        assert str(e.value) == "Subscriber not found."

    def test_extract_iccid_successful(self, cdr_service):
        result = cdr_service.extract_iccid(CDR_DATA_ROOT)
        assert result == "8944538532046590273"

    def test_extract_iccid_not_found(self, cdr_service):
        iccid = CDR_DATA_ROOT.find("SubscriberReference")
        CDR_DATA_ROOT.remove(iccid)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_iccid(CDR_DATA_ROOT)
        assert str(e.value) == "Subscriber reference not found."

    def test_extract_country_successful(self, cdr_service):
        result = cdr_service.extract_country(CDR_DATA_ROOT)
        assert result == "GBR"

    def test_extract_country_name_successful(self, cdr_service):
        result = cdr_service.extract_country_name(CDR_DATA_ROOT)
        assert result == "United Kingdom"

    def test_extract_carrier_successful(self, cdr_service):
        result = cdr_service.extract_carrier(CDR_DATA_ROOT)
        assert result == "GBRME"

    def test_extract_carrier_invalid(self, cdr_service):
        CDR_DATA_ROOT.find("Leg1TADIG").text = "123456"
        with pytest.raises(ValueError) as e:
            cdr_service.extract_carrier(CDR_DATA_ROOT)
        assert str(e.value) == "Invalid carrier 123456"

    def test_extract_carrier_not_found(self, cdr_service):
        carrier = CDR_DATA_ROOT.find("Leg1TADIG")
        CDR_DATA_ROOT.remove(carrier)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_carrier(CDR_DATA_ROOT)
        assert str(e.value) == "Carrier not found."

    def test_extract_leg_types_successful(self, cdr_service):
        result = cdr_service.extract_leg_types(CDR_DATA_ROOT)
        assert result == "gsm-data"

    def test_extract_leg_types_not_found(self, cdr_service):
        leg_type = CDR_DATA_ROOT.find("Type")
        CDR_DATA_ROOT.remove(leg_type)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_leg_types(CDR_DATA_ROOT)
        assert str(e.value) == "Leg type not found."

    def test_extract_cdr_data(self, cdr_service):
        result = cdr_service.extract_cdr_data(CDR_DATA_ROOT)
        assert isinstance(result, model.CDRData)

    def test_extract_cdr_data_bytes_not_found(self, cdr_service):
        connect_time = CDR_DATA_ROOT.find("Bytes")
        CDR_DATA_ROOT.remove(connect_time)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_data(CDR_DATA_ROOT)
        assert str(e.value) == "Bytes not found."

    def test_extract_cdr_data_duration_not_found(self, cdr_service):
        connect_time = CDR_DATA_ROOT.find("Duration")
        CDR_DATA_ROOT.remove(connect_time)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_data(CDR_DATA_ROOT)
        assert str(e.value) == "Duration not found."

    def test_extract_cdr_data_end_time_not_valid(self, cdr_service):
        CDR_DATA_ROOT.find("EndTime").text = None
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_data(CDR_DATA_ROOT)
        assert str(e.value) == "End time not valid."

    def test_extract_cdr_data_end_time_not_found(self, cdr_service):
        connect_time = CDR_DATA_ROOT.find("EndTime")
        CDR_DATA_ROOT.remove(connect_time)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_data(CDR_DATA_ROOT)
        assert str(e.value) == "End time not found."

    def test_extract_cdr_data_connect_time_not_valid(self, cdr_service):
        CDR_DATA_ROOT.find("ConnectTime").text = None
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_data(CDR_DATA_ROOT)
        assert str(e.value) == "Connect time not valid."

    def test_extract_cdr_data_connect_time_not_found(self, cdr_service):
        connect_time = CDR_DATA_ROOT.find("ConnectTime")
        CDR_DATA_ROOT.remove(connect_time)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_data(CDR_DATA_ROOT)
        assert str(e.value) == "Connect time not found."

    def test_extract_cdr_voice(self, cdr_service):
        result = cdr_service.extract_cdr_voice(CDR_VOICE_ROOT)
        assert isinstance(result, model.CDRVoice)

    def test_extract_cdr_voice_duration_not_found(self, cdr_service):
        duration = CDR_VOICE_ROOT.find("Duration")
        CDR_VOICE_ROOT.remove(duration)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_voice(CDR_VOICE_ROOT)
        assert str(e.value) == "Call minutes not found."

    def test_extract_cdr_voice_end_time_not_valid(self, cdr_service):
        CDR_VOICE_ROOT.find("EndTime").text = None
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_voice(CDR_VOICE_ROOT)
        assert str(e.value) == "End time not valid."

    def test_extract_cdr_voice_end_time_not_found(self, cdr_service):
        end_time = CDR_VOICE_ROOT.find("EndTime")
        CDR_VOICE_ROOT.remove(end_time)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_voice(CDR_VOICE_ROOT)
        assert str(e.value) == "End time not found."

    def test_extract_cdr_sms(self, cdr_service):
        result = cdr_service.extract_cdr_sms(CDR_SMS_ROOT)
        assert isinstance(result, model.CDRSMS)

    def test_extract_cdr_sms_duration_not_found(self, cdr_service):
        duration = CDR_SMS_ROOT.find("Duration")
        CDR_SMS_ROOT.remove(duration)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_sms(CDR_SMS_ROOT)
        assert str(e.value) == "Data sent not found."

    def test_extract_cdr_sms_end_time_not_valid(self, cdr_service):
        CDR_SMS_ROOT.find("EndTime").text = None
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_sms(CDR_SMS_ROOT)
        assert str(e.value) == "End time not valid."

    def test_extract_cdr_sms_end_time_not_found(self, cdr_service):
        end_time = CDR_SMS_ROOT.find("EndTime")
        CDR_SMS_ROOT.remove(end_time)
        with pytest.raises(ValueError) as e:
            cdr_service.extract_cdr_sms(CDR_SMS_ROOT)
        assert str(e.value) == "End time not found."

    def test_add_cdr_data(self, cdr_service):
        result = cdr_service.add_cdr(CDR_DATA_XML, None, "file_key.xml")
        assert isinstance(result, UUID)

    def test_add_cdr_voice(self, cdr_service):
        result = cdr_service.add_cdr(CDR_VOICE_XML, None, "file_key.xml")
        assert isinstance(result, UUID)

    def test_add_cdr_sms(self, cdr_service):
        result = cdr_service.add_cdr(CDR_SMS_XML, None, "file_key.xml")
        assert isinstance(result, UUID)

    def test_is_valid_xml_cdr_data(self, cdr_service):
        result = cdr_service.is_valid_xml_cdr(CDR_DATA_XML)
        assert result is True

    def test_is_valid_xml_cdr_voice(self, cdr_service):
        result = cdr_service.is_valid_xml_cdr(CDR_VOICE_XML)
        assert result is True

    def test_is_valid_xml_cdr_sms(self, cdr_service):
        result = cdr_service.is_valid_xml_cdr(CDR_SMS_XML)
        assert result is True

    def test_is_valid_xml_cdr_sms_invalid_leg_type(self, cdr_service):
        result = cdr_service.is_valid_xml_cdr(CDR_SMS_XML_INVALID)
        assert result is False

    def test_is_valid_xml_cdr_sms_invalid_xml(self, cdr_service):
        result = cdr_service.is_valid_xml_cdr(INVALID_XML_STRING)
        assert result is False

    def test_duplicate_cdr_success(self, cdr_service):
        start_date = "2024-03-15"
        end_date = "2024-03-31"
        expected = 10

        with patch.object(cdr_service, "get_duplicate_cdr") as mock_duplicate_cdr:
            mock_duplicate_cdr.return_value = expected
            result = cdr_service.get_duplicate_cdr(start_date, end_date)

            mock_duplicate_cdr.assert_called_once_with(start_date, end_date)
            assert result == expected

    def test_is_duplicate_cdr_success(self, cdr_service):
        cdr_service.cdr_repository.is_duplicate_cdr = MagicMock(
            return_value="2023-04-03 18:47:44"
        )
        result = cdr_service.is_duplicate_cdr(CDR_DATA_XML)
        assert result == "2023-04-03 18:47:44"

    def test_is_duplicate_cdr_unique_cdr(self, cdr_service):
        cdr_service.cdr_repository.is_duplicate_cdr = MagicMock(return_value=False)
        result = cdr_service.is_duplicate_cdr(CDR_DATA_XML)
        assert result is None

    def test_is_duplicate_cdr_non_data_cdr(self, cdr_service):
        cdr_service.cdr_repository.is_duplicate_cdr = MagicMock(return_value=False)
        result = cdr_service.is_duplicate_cdr(CDR_VOICE_XML)
        assert result is None

    def test_duplicate_cdr_invalid_date(self, cdr_service):

        duplicate_cdr = [
            {
                "imsi": "234588570010027",
                "session_starttime": "2024-03-16T10:30:00",
                "session_endtime": "2024-03-16T11:30:00",
                "duration": 3600,
                "data_volume": 1500,
                "num_duplicates": 2,
                "file_key": ["file1.xml", "file2.xml"],
            }
        ]
        cdr_service.cdr_repository.get_duplicate_cdr = MagicMock(
            return_value=duplicate_cdr
        )
        month = Month(2024, 4, 1)
        result = cdr_service.get_duplicate_cdr(month)
        assert len(result) == 1
        cdr_service.cdr_repository.get_duplicate_cdr.assert_called_once_with(month)
        assert result == duplicate_cdr


class TestCdrdataService(CdrdataContract):
    @pytest.fixture
    def sim_repository(self) -> InMemoryCdrRepository:
        return InMemoryCdrRepository()

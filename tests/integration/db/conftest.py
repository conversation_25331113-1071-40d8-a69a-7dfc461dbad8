from typing import Generator

import pytest
from sqlalchemy import text
from sqlalchemy.orm import Session

from app.db import base
from app.db.session import get_engine
from billing.adapters.procedures import Procedures
from common.db import Base


@pytest.fixture(scope="session")
def engine():
    engine = get_engine()
    yield engine


@pytest.fixture(scope="function")
def session(engine) -> Generator[Session, None, None]:
    with engine.connect() as connection:
        connection.execute(Procedures.drop_all_tables())
        connection.execute(Procedures.create_subscription_sim_allocation_procedure())
        connection.execute(Procedures.reset_invoice_allocation_details())
        connection.execute(text(Procedures.create_get_account_details()))
        connection.execute(Procedures.create_get_account_details_view())
    Base.metadata.create_all(bind=engine)
    session = Session(engine, autocommit=False, autoflush=False)
    yield session
    session.close()
    base.TEST_ENV = True
    Base.metadata.drop_all(bind=engine)
    with engine.connect() as connection:
        connection.execute(Procedures.delete_subscription_sim_allocation_procedure())
        connection.execute(Procedures.delete_reset_invoice_allocation_details())
        connection.execute(Procedures.delete_get_account_details_view())
        connection.execute(Procedures.delete_get_account_details())

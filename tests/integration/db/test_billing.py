import itertools
from datetime import datetime, timedelta
from decimal import Decimal
from operator import attrgetter
from random import choice, randint, randrange
from typing import Callable, Iterable
from unittest.mock import MagicMock

import pytest
from sqlalchemy import literal_column, select
from sqlalchemy.sql import func as sa_func

from accounts.adapters.repository import DatabaseAccountRepository
from accounts.domain.model import PaymentTerms
from billing.adapters.repository import (
    DatabaseBillingCycleRepository,
    InMemoryAccountRepository,
    InMemoryBillingCycleRepository,
)
from billing.constants import usage_ordering_fields_mapper
from billing.domain import model
from billing.domain.model import AdjustmentType, BillingCycle, SubscriptionSIM
from billing.exceptions import BillingCycleDoesNotExist
from billing.services import BillingService, SimUsageService, SqlSimUsageService
from common.ordering import Ordering
from common.types import IMSI, Month, Service, ServiceSet
from rate_plans.adapters.rate_plan_repository import DatabaseRatePlanRepository
from rating.adapters.usage_repository import (
    AbstractUsageRepository,
    DatabaseUsageRepository,
    InMemoryUsageRepository,
)
from rating.domain.model import MonthlyUsageRecords, RatePlan
from sim.adapters.externalapi import FakeAuditServiceAPI
from sim.adapters.orm import active_sim_monthly_statistic
from sim.adapters.repository import DatabaseSimRepository
from sim.domain.model import ICCID, SIMCard, SimStatus
from tests.unit.billing.test_services import (
    AbstractBillingServiceTestCase,
    BillingServiceContract,
)


class TestBillingCycleRepository:
    @pytest.fixture
    def repository(self, session) -> DatabaseBillingCycleRepository:
        return DatabaseBillingCycleRepository(session)

    def test_added_cycle_is_retrieved_by_month(self, repository):
        cycle = model.BillingCycle(Month.from_str("2022-11"))
        repository.add(cycle)

        saved = repository.get(cycle.month)

        assert cycle.month == saved.month

    def test_none_when_cycle_for_month_doesnt_exist(self, repository):
        result = repository.get(Month.from_str("2042-01"))

        assert result is None

    def test_error_when_remove_non_existing_cycle(self, repository):
        with pytest.raises(BillingCycleDoesNotExist):
            repository.remove(Month.from_str("2042-12"))

    def test_invoices_saved_with_billing_cycle(self, repository):
        cycle = model.BillingCycle(Month.from_str("2022-11"))
        cycle.make_invoice(1, 30)
        cycle.make_invoice(2, 30)
        repository.add(cycle)
        saved = repository.get(cycle.month)
        assert len(saved._invoices) == 2
        assert saved.get_invoice(1).id is not None
        assert saved.get_invoice(2).id is not None
        assert saved.get_invoice(1).account_id == 1
        assert saved.get_invoice(2).account_id == 2

    def test_added_adjustment_saved_with_invoice(self, repository):
        cycle = model.BillingCycle(Month.from_str("2022-11"))
        invoice = cycle.make_invoice(1, 30)
        repository.add(cycle)

        adjustment = model.Adjustment(
            id=None,
            date=datetime.today(),
            type=AdjustmentType.SMS_CHARGE_ADJUSTMENT,
            amount=Decimal("10.12"),
        )
        invoice.rating_state = model.InvoiceRatingState.GENERATED
        invoice.add_adjustment(adjustment)
        repository.update_invoice(invoice)
        updated = repository.get_invoice_by_id(invoice.id)
        assert len(updated.adjustments) == 1
        assert updated.adjustments[0].id is not None
        assert updated.adjustments[0].amount == adjustment.amount

    def test_modified_adjustment_saved_with_invoice(self, repository):
        cycle = model.BillingCycle(Month.from_str("2022-11"))
        invoice = cycle.make_invoice(1, 30)
        invoice.rating_state = model.InvoiceRatingState.GENERATED
        repository.add(cycle)
        invoice.add_adjustment(
            model.Adjustment(
                id=None,
                date=datetime.today(),
                type=AdjustmentType.SMS_CHARGE_ADJUSTMENT,
                amount=Decimal("10.12"),
            )
        )
        repository.update_invoice(invoice)
        old = repository.get_invoice_by_id(invoice.id).adjustments[0]
        new = model.Adjustment(
            id=old.id,
            date=datetime.today() + timedelta(days=3),
            type=AdjustmentType.SIM_FEE,
            amount=Decimal("10.12"),
        )

        invoice.remove_adjustment(old)
        invoice.add_adjustment(new)

        updated_invoice = repository.get_invoice_by_id(invoice.id)
        assert len(updated_invoice.adjustments) == 1
        assert updated_invoice.adjustments[0].type == new.type
        assert updated_invoice.adjustments[0].id == old.id == new.id

    def test_filter_invoices_by_accounts(self, repository):
        cycle = model.BillingCycle(Month.from_str("2022-11"))
        cycle.make_invoice(1, 30)
        cycle.make_invoice(2, 30)
        repository.add(cycle)
        invoices = repository.get_invoices(account_ids=[1])
        assert len(list(invoices)) == 1
        invoices = repository.get_invoices(account_ids=[1, 2])
        assert len(list(invoices)) == 2

    def test_get_invoices_new_with_success(self, repository):
        cycle = model.BillingCycle(Month.from_str("2025-04"))
        cycle.make_invoice(1, 30)
        repository.add(cycle)
        billing_cycle = repository.get(cycle.month)

        # Arrange
        mock_session = MagicMock()

        # Sample billing_cycle and month input
        # billing_cycle = BillingCycle(id=101)
        month = Month(2025, 4, 1)

        # Mock output of each function
        mock_invoice_summary = [
            (
                13,
                1,
                "account1",
                "/home/<USER>/tmp/monoglass/src/images/bt-logo.svg",
                "ACTIVE",
                True,
                "AEROSPACE",
                "STR",
                "2027-6-27",
                month,
                None,
                None,
                False,
                "GENERATED",
                Decimal("0"),
                0,
            ),
            (
                14,
                2,
                "account2",
                "/home/<USER>/tmp/monoglass/src/images/bt-logo.svg",
                "ACTIVE",
                True,
                "AEROSPACE",
                "STR",
                "2027-6-27",
                month,
                None,
                None,
                False,
                "GENERATED",
                Decimal("0"),
                0,
            ),
        ]

        mock_adjustments = [
            (1, 13, "2025-4-18", Decimal("170.5"), "OTHER_CHARGE_ONE_TIME")
        ]

        mock_subscriptions = [
            (
                346,
                13,
                221,
                "5_Pay NEW ********",
                Decimal("12.03"),
                0,
                Decimal("12.3499999999999996447286321199499070644378662109375"),
                0,
            ),
            (
                347,
                14,
                222,
                "6_Pay NEW ********",
                Decimal("12.03"),
                0,
                Decimal("12.3499999999999996447286321199499070644378662109375"),
                0,
            ),
        ]

        mock_imsi_usage = [
            (
                13,
                "VOICE_MO",
                Decimal("0.000000"),
                Decimal("36"),
                Decimal("0"),
                Decimal("0"),
            ),
            (
                13,
                "VOICE_MT",
                Decimal("0.000000"),
                Decimal("39"),
                Decimal("0E-26"),
                Decimal("0"),
            ),
            (
                13,
                "SMS_MO",
                Decimal("0.000000"),
                Decimal("45"),
                Decimal("0.00"),
                Decimal("0"),
            ),
            (13, "SMS_MT", Decimal("0"), Decimal("4503"), Decimal("0"), Decimal("0")),
            (
                13,
                "DATA",
                Decimal("0.030762"),
                Decimal("157286697"),
                Decimal("0"),
                Decimal("0"),
            ),
            (
                14,
                "VOICE_MO",
                Decimal("0.000000"),
                Decimal("36"),
                Decimal("0"),
                Decimal("0"),
            ),
            (
                14,
                "VOICE_MT",
                Decimal("0.000000"),
                Decimal("39"),
                Decimal("0E-26"),
                Decimal("0"),
            ),
            (
                14,
                "SMS_MO",
                Decimal("0.000000"),
                Decimal("45"),
                Decimal("0.00"),
                Decimal("0"),
            ),
            (14, "SMS_MT", Decimal("0"), Decimal("4503"), Decimal("0"), Decimal("0")),
            (
                14,
                "DATA",
                Decimal("0.030762"),
                Decimal("157286697"),
                Decimal("0"),
                Decimal("0"),
            ),
        ]

        # Patch `func` calls to return mocked select queries (not executed)
        sa_func.get_invoice_summary = MagicMock(
            return_value=select([literal_column("*")])
        )
        sa_func.get_invoice_adjustments = MagicMock(
            return_value=select([literal_column("*")])
        )
        sa_func.get_invoice_subscriptions = MagicMock(
            return_value=select([literal_column("*")])
        )
        sa_func.get_imsi_usage = MagicMock(return_value=select([literal_column("*")]))

        # Patch session.execute(...).fetchall() to return the mock results
        mock_session.execute.side_effect = [
            MagicMock(fetchall=MagicMock(return_value=mock_invoice_summary)),
            MagicMock(fetchall=MagicMock(return_value=mock_adjustments)),
            MagicMock(fetchall=MagicMock(return_value=mock_subscriptions)),
            MagicMock(fetchall=MagicMock(return_value=mock_imsi_usage)),
        ]

        # Mock repository with the mocked session
        repo = DatabaseBillingCycleRepository(session=mock_session)

        # Patch the `map_invoices` method to simplify output verification
        # repo.map_invoices = MagicMock(return_value=["mocked_invoice"])

        # Act
        result = repo.get_invoices_new(billing_cycle=billing_cycle, month=month)

        # Assert
        assert len(result) == 2
        assert result[0].id == 13
        assert mock_session.execute.call_count == 4  # One for each function


class TestOrderingSubscriptionSimsSqlRepository:
    @pytest.fixture
    def repository(self, session) -> DatabaseBillingCycleRepository:
        return DatabaseBillingCycleRepository(session)

    @pytest.fixture(params=["ASC", "DESC"])
    def sorting_order(self, request):
        return request.param

    @pytest.fixture
    def make_billing_cycle_filled_with_data(self, repository):
        def _factory(
            month_string: str = "2023-05",
            account_ids: Iterable[int] = range(10),
            subscription_per_invoice: int = 2,
            imsis_for_subscription: int = 10,
            imsi_first: int = 100,
        ) -> model.BillingCycle:

            billing_cycle = model.BillingCycle(Month.from_str(month_string))
            for i in account_ids:
                billing_cycle.make_invoice(i, PaymentTerms(30))
            repository.add(billing_cycle)

            for invoice in billing_cycle.invoices:
                for i in range(subscription_per_invoice):
                    invoice.add_subscription(
                        i,
                        f"Test plan - {i}",
                        access_fee=Decimal(i + 0.23),
                        sims_total=imsis_for_subscription,
                        sim_charge=Decimal(i + 0.23),
                    )
                    [
                        invoice.add_sim(  # type: ignore
                            rate_plan_id=i,
                            sim_id=int(imsi),
                            imsi=imsi,
                            iccid=ICCID(f"0000{imsi}"),
                            msisdn=imsi,
                            first_time_activated=True,
                            sim_status=SimStatus.ACTIVE,
                        )
                        for imsi in map(
                            IMSI.from_int,
                            range(imsi_first, imsi_first + imsis_for_subscription),
                        )
                    ]
                    imsi_first += imsis_for_subscription

            repository.add(billing_cycle)

            return billing_cycle

        return _factory

    @staticmethod
    def _get_sims_and_check_ordering(
        repository, invoice, ordering, check_order, order_direction
    ):
        subscription_sims = list(
            repository.get_subscription_sims(invoice.id, ordering=ordering)
        )

        assert len(subscription_sims) == sum(len(i.sims) for i in invoice.subscriptions)
        assert subscription_sims == sorted(
            subscription_sims,
            key=lambda sim: check_order(sim),
            reverse=(order_direction == "DESC"),
        )

    @pytest.mark.parametrize(
        "order_field,check_order_field",
        [
            ("iccid", "iccid"),
            ("id", "sim_id"),
            ("msisdn", "msisdn"),
            ("imsi", "imsi"),
        ],
    )
    def test_ordering_by_sim_field(
        self,
        repository,
        make_billing_cycle_filled_with_data,
        order_field,
        check_order_field,
        sorting_order,
    ):
        billing_cycle = make_billing_cycle_filled_with_data(account_ids=(2,))
        invoice = billing_cycle.invoices[0]

        self._get_sims_and_check_ordering(
            repository=repository,
            invoice=invoice,
            ordering=Ordering(
                field=order_field, order=sorting_order, model=SubscriptionSIM
            ),
            check_order=attrgetter(check_order_field),
            order_direction=sorting_order,
        )

    @pytest.mark.parametrize(
        "order_field,check_order",
        [
            ("rate_plan_name", attrgetter("subscription.name")),
            ("subscription_charge", attrgetter("subscription.access_fee")),
        ],
    )
    def test_ordering_by_subscription_field(
        self,
        repository,
        make_billing_cycle_filled_with_data,
        order_field,
        check_order,
        sorting_order,
    ):
        billing_cycle = make_billing_cycle_filled_with_data(
            account_ids=(4,), subscription_per_invoice=4, imsis_for_subscription=5
        )
        invoice = billing_cycle.invoices[0]

        self._get_sims_and_check_ordering(
            repository=repository,
            invoice=invoice,
            ordering=Ordering(
                field=order_field, order=sorting_order, model=SubscriptionSIM
            ),
            check_order=check_order,
            order_direction=sorting_order,
        )

    @pytest.mark.parametrize(
        "order_field",
        [
            "data_charge",
            "voice_charge",
            "sms_charge",
            "data_volume",
            "sms_volume",
            "sms_mo_volume",
            "sms_mt_volume",
            "voice_volume",
            "voice_mo_volume",
            "voice_mt_volume",
        ],
    )
    def test_ordering_by_usage_field(
        self,
        repository,
        make_billing_cycle_filled_with_data,
        order_field,
        sorting_order,
    ):
        billing_cycle = make_billing_cycle_filled_with_data(
            account_ids=(2,), subscription_per_invoice=2, imsis_for_subscription=15
        )
        invoice = billing_cycle.invoices[0]

        def check_order_rule(services: ServiceSet, field: str) -> Callable:
            return lambda sim: sum(
                [getattr(i, field) for i in sim.usage if i.service in services]
            )

        ordering_field_mapper = usage_ordering_fields_mapper[order_field]
        usage_records = [
            model.SimUsage(
                imsi=sim.imsi,
                service=choice(list(ordering_field_mapper.services)),
                volume=randint(200, 10000),
                charge=Decimal(randint(5, 400) / 1000),
            )
            for sim in itertools.chain.from_iterable(
                [s.sims for s in invoice.subscriptions]
            )
        ]
        billing_cycle.insert_sim_usage(usage_records)
        repository.add(billing_cycle)

        check_order = check_order_rule(
            ordering_field_mapper.services, order_field.rsplit("_", 1)[-1]
        )

        self._get_sims_and_check_ordering(
            repository=repository,
            invoice=invoice,
            ordering=Ordering(
                field=order_field, order=sorting_order, model=SubscriptionSIM
            ),
            check_order=check_order,
            order_direction=sorting_order,
        )


class SimUsageServiceContract:
    @pytest.fixture
    def usage_repository(self):
        raise NotImplementedError

    @pytest.fixture
    def billing_cycle_repository(self):
        raise NotImplementedError

    @pytest.fixture
    def sim_usage_service(self):
        raise NotImplementedError

    @pytest.fixture
    def make_usage(self):
        def _factory(imsi, month, volume, operator="DUMMY", service=Service.DATA):
            return MonthlyUsageRecords(
                IMSI(f"{imsi:0<15}"), service, operator, month, volume
            )

        return _factory

    @pytest.fixture
    def make_sim_usage(self):
        def _factory(imsi, volume, charge, service=Service.DATA):
            return model.SimUsage(imsi, service, volume, charge)

        return _factory

    @pytest.mark.parametrize("copy_with_imsi", [False, True])
    def test_copy_usage_for_billing(
        self,
        sim_usage_service,
        usage_repository,
        billing_cycle_repository,
        make_usage,
        copy_with_imsi,
    ):
        prev_month = Month.from_str("2022-11")
        billing_month = Month.from_str("2022-12")
        next_month = Month.from_str("2023-01")
        usage = [
            make_usage(1, prev_month, 10),
            make_usage(1, billing_month, 15, operator="ONEOP"),
            make_usage(1, billing_month, 5, operator="TWOOP"),
            make_usage(1, next_month, 30),
            make_usage(2, prev_month, 100),
            make_usage(2, billing_month, 200),
            make_usage(2, next_month, 300),
        ]
        usage_repository.bulk_insert(usage)
        bc = BillingCycle(billing_month)
        billing_cycle_repository.add(bc)
        bc = billing_cycle_repository.get(bc.month)
        assert not any(bc.sim_usage)

        if copy_with_imsi:
            imsi_list = [u.imsi for u in usage if u.month == bc.month]
            sim_usage_service.copy_usage_for_billing(bc, imsis=imsi_list)
        else:
            sim_usage_service.copy_usage_for_billing(bc)

        bc = billing_cycle_repository.get(bc.month)
        sim_usage = list(bc.sim_usage)
        assert len(sim_usage) == 2
        assert sim_usage[0].volume in [20, 200]

    def test_remove_billing_usage(
        self, billing_cycle_repository, make_sim_usage, sim_usage_service
    ):
        sim_usage_imsis = [IMSI(f"{x:0<15}") for x in range(10)]
        billing_month = Month.from_str("2022-11")
        bc = BillingCycle(billing_month)
        bc.insert_sim_usage([make_sim_usage(imsi, 10, 1.1) for imsi in sim_usage_imsis])
        billing_cycle_repository.add(bc)
        bc = billing_cycle_repository.get(bc.month)
        sim_usage = list(bc.sim_usage)
        assert len(sim_usage) == 10

        imsis_to_delete = sim_usage_imsis[: randrange(1, len(sim_usage_imsis))]
        sim_usage_service.remove_billing_usage(bc, imsis_to_delete)

        bc = billing_cycle_repository.get(bc.month)
        updated_sim_usage = list(bc.sim_usage)
        updated_sim_usage_imsis = [su.imsi for su in updated_sim_usage]
        diff = set(sim_usage_imsis).difference(set(updated_sim_usage_imsis))
        assert diff == set(imsis_to_delete)


class TestSimUsageServiceWithDatabaseRepository(SimUsageServiceContract):
    @pytest.fixture
    def usage_repository(self, session):
        return DatabaseUsageRepository(session)

    @pytest.fixture
    def billing_cycle_repository(self, session):
        return DatabaseBillingCycleRepository(session)

    @pytest.fixture
    def sim_usage_service(self, usage_repository, billing_cycle_repository):
        return SimUsageService(usage_repository, billing_cycle_repository)


class TestSimUsageWithInMemoryRepository(SimUsageServiceContract):
    @pytest.fixture
    def usage_repository(self):
        return InMemoryUsageRepository()

    @pytest.fixture
    def billing_cycle_repository(self):
        return InMemoryBillingCycleRepository()

    @pytest.fixture
    def sim_usage_service(self, usage_repository, billing_cycle_repository):
        return SimUsageService(usage_repository, billing_cycle_repository)


class TestSqlSimUsageService(SimUsageServiceContract):
    @pytest.fixture
    def usage_repository(self, session):
        return DatabaseUsageRepository(session)

    @pytest.fixture
    def billing_cycle_repository(self, session):
        return DatabaseBillingCycleRepository(session)

    @pytest.fixture
    def sim_usage_service(self, session):
        return SqlSimUsageService(session)


class BillingServiceDatabaseFixtureMixin(AbstractBillingServiceTestCase):
    @pytest.fixture
    def usage_repository(self, session) -> AbstractUsageRepository:
        return DatabaseUsageRepository(session)

    @pytest.fixture
    def sim_repository(self, session) -> DatabaseSimRepository:
        return DatabaseSimRepository(session)

    @pytest.fixture
    def billing_cycle_repository(self, session):
        return DatabaseBillingCycleRepository(session)

    @pytest.fixture
    def account_repository(self, session):
        return DatabaseAccountRepository(session)

    @pytest.fixture
    def add_sims_to_active_in_month(self, sim_repository):
        def factory(
            rate_plan: RatePlan,
            month: Month,
            sim_cards: Iterable[SIMCard],
            first_time_activated: bool = False,
        ):
            values = [
                {
                    "sim_card_id": sim.id,
                    "month": month,
                    "sim_status": SimStatus.ACTIVE,
                    "is_first_activation": first_time_activated,
                }
                for sim in sim_cards
            ]
            sim_repository.session.execute(active_sim_monthly_statistic.insert(values))

        return factory

    @pytest.fixture
    def sim_usage_service(self, usage_repository, session, billing_cycle_repository):
        return SimUsageService(usage_repository, billing_cycle_repository)

    @pytest.fixture
    def audit_service(self) -> FakeAuditServiceAPI:
        return FakeAuditServiceAPI()

    @pytest.fixture
    def billing_service(
        self,
        session,
        billing_cycle_repository,
        accounts,
        sim_usage_service,
        audit_service,
    ):
        return BillingService(
            account_repository=InMemoryAccountRepository(accounts),
            billing_cycle_repository=billing_cycle_repository,
            rate_plan_repository=DatabaseRatePlanRepository(session),
            sim_repository=DatabaseSimRepository(session),
            sim_usage_service=sim_usage_service,
            audit_service=audit_service,
        )


class TestBillingServiceWithDatabase(
    BillingServiceDatabaseFixtureMixin, BillingServiceContract
):
    ...


class TestBillingServiceWithDatabaseAndSqlSimUsage(TestBillingServiceWithDatabase):
    @pytest.fixture
    def sim_usage_service(self, session):
        return SqlSimUsageService(session)

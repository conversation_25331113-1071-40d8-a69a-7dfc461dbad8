import unittest
from unittest.mock import MagicMock, Mock

import pytest
from fastapi import BackgroundTasks, HTTPException, Request, status
from fastapi.testclient import TestClient

from api.authorization.examples import PERMISSIONS
from api.sim.endpoints import (
    create_empty_range,
    create_range,
    delete_range,
    get_ranges,
    get_sim_remains,
    get_sim_status,
    remove_allocation,
    remove_allocations,
)
from api.sim.examples import CREATE_RANGE_REQUEST, RANGES
from api.sim.schemas import BackgroundProcessResponse, SIMCardsRemains
from app.main import app
from authorization.domain.ports import AbstractAuthorizationAPI
from sim.domain import model
from sim.exceptions import (
    AllocationDeletionNotAllowed,
    AllocationDoesNotExist,
    PplUnknownSubscriber,
    RangeDoesNotExist,
    RangeIntegrityError,
    SimCardsNotFound,
)
from sim.services import AbstractSimService


class FormFactor:
    MICRO = "micro"
    NANO = "nano"

    def __str__(self):
        return self.name


class TestGetRanges(unittest.TestCase):
    def test_get_ranges_success(self):
        # Arrange
        mock_sim_service = Mock()
        mock_ranges = RANGES
        mock_sim_service.get_ranges.return_value = mock_ranges

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = "https://testclient.com/v1/glass/sim/ranges"
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Act
        result = get_ranges(
            request=mock_request, sim_service=mock_sim_service, authorization=mock_auth
        )

        # Assert
        self.assertEqual(list(result), mock_ranges)

    def test_get_ranges_empty_result(self):
        # Arrange
        mock_sim_service = Mock()
        mock_sim_service.get_ranges.return_value = []

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = "https://testclient.com/v1/glass/sim/ranges"
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Act
        result = get_ranges(
            request=mock_request, sim_service=mock_sim_service, authorization=mock_auth
        )

        # Assert
        self.assertEqual(list(result), [])

    def test_get_ranges_error(self):
        # Arrange
        mock_sim_service = Mock()
        mock_sim_service.get_ranges.side_effect = HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = "https://testclient.com/v1/glass/sim/ranges"
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Act and Assert
        with self.assertRaises(HTTPException) as context:
            get_ranges(
                request=mock_request,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        self.assertEqual(context.exception.status_code, 500)
        self.assertEqual(context.exception.detail, "Internal Server Error")

    @pytest.mark.skip(reason="Dictionary object issue")
    def test_create_empty_range_success(self):
        # Arrange
        mock_sim_service = Mock(spec=AbstractSimService)
        create_range_request = CREATE_RANGE_REQUEST
        expected_result = RANGES

        # Act

        result = create_empty_range(
            range_in=dict(create_range_request), sim_service=mock_sim_service
        )

        range_in_data = {"title": "Reference", "formFactor": "MICRO"}

        # Assert
        mock_sim_service.create_empty_range.assert_called_once_with(
            range_in_data, created_by="John Billing"
        )
        self.assertEqual(result, expected_result)

    def test_create_range_success(self):
        # Create a mock for the sim_service
        mock_sim_service = MagicMock()

        # Mock the validate_file_size method to do nothing
        mock_sim_service.validate_file_size.return_value = None

        # Mock the validate_sim_cards method to return a list of sim cards
        mock_sim_service.validate_sim_cards.return_value = [
            model.SIMCard(
                imsi="123456789012345",
                iccid="12345678901234567890",
                msisdn="1234567890",
            )
        ]

        # Mock the add_upload_file_status method
        mock_sim_service.add_upload_file_status.return_value = None

        # Create a mock for the background_tasks
        background_tasks = MagicMock(spec=BackgroundTasks)

        # Create a mock for the authorization
        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        # Create a mock for the request
        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/documents"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "POST"
        mock_request.client.host = "127.0.0.1"
        mock_request.scope = {"endpoint": MagicMock(__name__="test_endpoint")}

        # Create a mock for the form_data
        form_data = MagicMock()
        form_data.title = "Test Range"
        form_data.form_factor = "MICRO"

        # Make form_data.file.file iterable
        mock_file = MagicMock()
        mock_file.__iter__.return_value = iter(
            [
                b"IMSI,ICCID,MSISDN\n",
                b"123456789012345,12345678901234567890,123456789\n",
            ]
        )
        form_data.file.file = mock_file

        # Call the function
        response = create_range(
            request=mock_request,
            background_tasks=background_tasks,
            form_data=form_data,
            sim_service=mock_sim_service,
            authorization=mock_auth,
        )

        # Verify the response
        assert isinstance(response, BackgroundProcessResponse)
        assert response.message == "We have received your request."
        assert hasattr(response, "request_id")
        assert response.request_id is not None

    def test_create_range_failure_sim_error(self):
        # Mock dependencies to raise a SimError
        mock_sim_service = Mock()
        mock_sim_service.create_range.side_effect = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND
        )

        # Create test client and call the endpoint
        client = TestClient(app)
        response = client.post(
            "/create_range", json={"title": "Test Range", "form_factor": "Micro"}
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND

        # Check that the response body contains the error message from SimError
        assert response.json() == {"detail": "Not Found"}

    def test_create_range_failure_parsing_error(self):
        # Mock dependencies
        mock_sim_service = MagicMock()
        mock_sim_service.validate_file_size.side_effect = Exception(
            "Invalid form factor"
        )

        # Create a mock for the background_tasks
        background_tasks = MagicMock(spec=BackgroundTasks)

        # Create a mock for the authorization
        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        # Create a mock for the request
        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = "https://test.com/v1/glass/sim/documents"
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "POST"
        mock_request.client.host = "127.0.0.1"
        mock_request.scope = {"endpoint": MagicMock(__name__="test_endpoint")}

        # Create a mock for the form_data
        form_data = MagicMock()
        form_data.title = "Test Range"
        form_data.form_factor = "INVALID_FORM_FACTOR"

        # Make form_data.file.file iterable
        mock_file = MagicMock()
        mock_file.__iter__.return_value = iter(
            [
                b"IMSI,ICCID,MSISDN\n",
                b"123456789012345,12345678901234567890,123456789\n",
            ]
        )
        form_data.file.file = mock_file

        # Call the function and expect a 400 error
        with pytest.raises(HTTPException) as exc_info:
            create_range(
                request=mock_request,
                background_tasks=background_tasks,
                form_data=form_data,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        # Check that the response has a 400 status code
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST

        # Check that the response body contains the expected error message
        assert exc_info.value.detail == "We Couldn't process the request"

    def test_get_sim_status_success(self):
        # Mock dependencies
        mock_sim_service = Mock()
        imsi = "123456789012345"
        mock_sim_service.sim_status.return_value = model.SIMStatusResponse(
            reference_id="123",
            imsi="123456789012345",
            msisdn="9876543210",
            sim_status="ACTIVE",
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/cards/status/{imsi}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Call the function
        response = get_sim_status(
            request=mock_request,
            imsi=imsi,
            sim_service=mock_sim_service,
            authorization=mock_auth,
        )

        # Check that the response is successful
        # assert response == model.SIMStatusResponse
        assert response.reference_id == 123
        assert response.imsi == "123456789012345"
        assert response.msisdn == "9876543210"
        assert response.sim_status == "ACTIVE"

    def test_get_sim_status_sim_cards_not_found(self):
        # Mock dependencies to raise SimCardsNotFound
        mock_sim_service = Mock()
        imsi = "123456789012345"
        mock_sim_service.sim_status.side_effect = SimCardsNotFound(
            "SIM cards not found"
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/cards/status/{imsi}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            get_sim_status(
                request=mock_request,
                imsi=imsi,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "SIM cards not found"

    def test_get_sim_status_unknown_subscriber(self):
        # Mock dependencies to raise PplUnknownSubscriber
        mock_sim_service = Mock()
        imsi = "123456789012345"
        mock_sim_service.sim_status.side_effect = PplUnknownSubscriber(imsi)

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/cards/status/{imsi}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Call the function and expect a 400 error
        with pytest.raises(HTTPException) as exc_info:
            get_sim_status(
                request=mock_request,
                imsi=imsi,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "123456789012345 IMSI not allocated to BT."

    def test_delete_range_success(self):
        # Mock dependencies
        mock_sim_service = Mock()
        range_id = 1

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/ranges/{id}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function
        delete_range(
            request=mock_request,
            range_id=range_id,
            sim_service=mock_sim_service,
            authorization=mock_auth,
        )

        # Check that the mock service was called with the correct parameters
        mock_sim_service.remove_range.assert_called_once_with(range_id)

    def test_delete_range_not_found(self):
        # Mock dependencies to raise RangeDoesNotExist
        mock_sim_service = Mock()
        range_id = 1
        mock_sim_service.remove_range.side_effect = RangeDoesNotExist(range_id)

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/ranges/{id}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            delete_range(
                request=mock_request,
                range_id=range_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Range with id:1 does not exist."

    def test_delete_range_conflict(self):
        # Mock dependencies to raise RangeIntegrityError
        mock_sim_service = Mock()
        range_id = 1
        mock_sim_service.remove_range.side_effect = RangeIntegrityError(
            "Range integrity error"
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/ranges/{id}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 409 error
        with pytest.raises(HTTPException) as exc_info:
            delete_range(
                request=mock_request,
                range_id=range_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "Range integrity error"

    def test_remove_allocations_success(self):
        # Mock dependencies
        mock_sim_service = Mock()
        range_id = 1
        mock_sim_service.remove_allocations_by_range_id.return_value = None

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/allocations"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function
        result = remove_allocations(
            request=mock_request,
            range_id=range_id,
            sim_service=mock_sim_service,
            authorization=mock_auth,
        )

        # Check that the mock service was called with the correct parameters
        mock_sim_service.remove_allocations_by_range_id.assert_called_once_with(
            range_id
        )

        # Check that the result is None (as indicated by the function signature)
        assert result is None

    def test_remove_allocations_not_found(self):
        # Mock dependencies to raise RangeDoesNotExist
        mock_sim_service = Mock()
        range_id = 5
        mock_sim_service.remove_allocations_by_range_id.side_effect = RangeDoesNotExist(
            range_id
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/allocations"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            remove_allocations(
                request=mock_request,
                range_id=range_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Range with id:5 does not exist."

    def test_remove_allocations_not_found_with_404(self):
        # Mock dependencies to raise RangeDoesNotExist
        mock_sim_service = Mock()
        allocation_id = 1
        mock_sim_service.remove_allocation.side_effect = AllocationDoesNotExist(
            allocation_id
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/allocations"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            remove_allocation(
                request=mock_request,
                allocation_id=allocation_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_remove_allocations_not_found_with_400(self):
        mock_sim_service = Mock()
        range_id = 1
        allocation_id = 1
        mock_sim_service.remove_allocation.side_effect = AllocationDeletionNotAllowed(
            range_id, allocation_id
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/allocations"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            remove_allocation(
                request=mock_request,
                allocation_id=allocation_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            exc_info.value.detail
            == "Allocation with id: 1 must be the last in the range with id: 1."
        )

    @pytest.mark.skip("Need to fix")
    def test_get_sim_remains_success(self):
        # Mock dependencies
        mock_sim_service = Mock()
        mock_sim_service.get_sim_remains.return_value = [
            ("Provider1", FormFactor.MICRO, 10),
            ("Provider1", FormFactor.NANO, 20),
            ("Provider2", FormFactor.MICRO, 5),
        ]

        # Call the function
        response = get_sim_remains(sim_service=mock_sim_service)

        # Check that the response is a list of SIMCardsRemains
        assert isinstance(response, list)
        for item in response:
            assert isinstance(item, SIMCardsRemains)

        # Check that the values are correct based on the mock data
        assert response == [
            SIMCardsRemains(provider="Provider1", micro=10, nano=20),
            # nano is not present in this case
            SIMCardsRemains(provider="Provider2", micro=5, nano=None),
        ]

    def test_get_sim_remains_with_404(self):
        mock_sim_service = Mock()

        def override_dependency():
            return mock_sim_service

        with TestClient(app) as client:
            app.dependency_overrides[mock_sim_service.sim_service] = override_dependency

            response = client.get("/cards/remains")
            assert response.status_code == 404

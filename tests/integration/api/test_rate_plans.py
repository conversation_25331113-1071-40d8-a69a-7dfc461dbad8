from copy import deepcopy
from unittest.mock import MagicMock

from fastapi import HTT<PERSON><PERSON>x<PERSON>, status

from api.rate_plans import schemas
from api.rate_plans.deps import (
    account_resolver,
    fake_account_resolver,
    fake_media_service,
    get_media_service,
    get_rate_plan_repository,
    get_sim_repository,
)
from api.rate_plans.endpoints import (
    create_rate_plan_model,
    delete_rate_plan_model,
    get_rate_plans_model_by_id,
    get_rate_plans_models,
    rate_plans_by_accounts,
    update_rate_plans_model_by_id,
)
from api.rate_plans.examples import (
    ACCOUNT_RATE_PLAN,
    CREATE_UPDATE_RATE_MODEL,
    CREATE_UPDATE_RATE_PLAN_MODEL,
    GET_RATE_PLAN_MODELS,
    RATE_PLAN_REQUEST,
)
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    InMemoryRatePlanRepository,
)
from rate_plans.domain import model
from rate_plans.exceptions import RatePlanModelDeletionError, RatePlanModelNotFound
from rate_plans.services import AbstractRatePlanService
from sim.adapters.repository import AbstractSimRepository, InMemorySimRepository

import pytest  # isort: skip


@pytest.fixture
def repository() -> AbstractRatePlanRepository:
    return InMemoryRatePlanRepository(rate_plans=[])


@pytest.fixture
def sim_repository() -> AbstractSimRepository:
    return InMemorySimRepository()


@pytest.fixture(autouse=True)
def override_dependencies(client, repository, sim_repository):
    overrides = client.app.dependency_overrides
    backup_deps = deepcopy(overrides)
    overrides[get_rate_plan_repository] = lambda: repository
    client.app.dependency_overrides[account_resolver] = fake_account_resolver
    overrides[get_media_service] = fake_media_service
    overrides[get_sim_repository] = lambda: sim_repository
    yield
    client.app.dependency_overrides = backup_deps


@pytest.fixture
def update_rate_plan(api_factory):
    def factory(rate_plan_id, **extra_fields):
        request = {**RATE_PLAN_REQUEST, **extra_fields, "id": rate_plan_id}
        return api_factory(
            "update_rate_plan",
            method="PUT",
            request_kwargs={"json": request},
            url_kwargs={"id": rate_plan_id},
        )

    return factory


@pytest.fixture
def mock_rate_plan_service():
    mock_service = MagicMock(spec=AbstractRatePlanService)

    rate_models = list(
        map(lambda rate_model: model.RateModel(**rate_model), GET_RATE_PLAN_MODELS)
    )
    mock_service.get_rate_plans_models.return_value = model.RatePlanModels(
        result=rate_models
    )
    mock_service.get_rate_plans_model_by_id.return_value = rate_models[1]

    create_updateed_rate_model = model.RateModel(**CREATE_UPDATE_RATE_MODEL)
    mock_service.update_rate_plans_model_by_id.return_value = create_updateed_rate_model
    mock_service.create_rate_plan_model.return_value = create_updateed_rate_model
    mock_service.delete_rate_plan_model.return_value = None

    account_rate_plan = ACCOUNT_RATE_PLAN
    rate_plan_details = list(
        map(
            lambda rate_plan: model.AccountRatePlan(**rate_plan),
            account_rate_plan["rate_plans"],
        )
    )
    mock_service.rate_plans_by_accounts.return_value = (
        [
            model.AccountRatePlanDetails(
                account_id=account_rate_plan["account_id"],
                account_name=account_rate_plan["account_name"],
                logo_url=account_rate_plan["logo_url"],
                rate_plans=rate_plan_details,
            )
        ],
        1,
    )
    return mock_service


@pytest.fixture
def mock_rate_plan_error_service():
    def auth_factory(error_type, message):
        mock_error_service = MagicMock(spec=AbstractRatePlanService)

        rate_models = list(
            map(lambda rate_model: model.RateModel(**rate_model), GET_RATE_PLAN_MODELS)
        )
        mock_error_service.get_rate_plans_models.return_value = model.RatePlanModels(
            result=rate_models
        )

        mock_error_service.get_rate_plans_models.side_effect = error_type(message)

        mock_error_service.get_rate_plans_model_by_id.return_value = rate_models[1]

        mock_error_service.get_rate_plans_model_by_id.side_effect = error_type(message)

        mock_error_service.update_rate_plans_model_by_id.side_effect = error_type(
            message
        )
        mock_error_service.create_rate_plan_model.side_effect = error_type(message)
        mock_error_service.delete_rate_plan_model.side_effect = error_type(message)
        return mock_error_service

    return auth_factory


class TestRatePlan:
    def test_create_rate_plan(
        self, create_rate_plan_api_factory, create_distributor_client_user
    ):
        distributor_client_user = create_distributor_client_user()
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        assert response.status_code == status.HTTP_201_CREATED

    def test_update_rate_plan_with_error(
        self, update_rate_plan, create_distributor_client_user
    ):
        distributor_client_user = create_distributor_client_user()
        response = update_rate_plan(distributor_client_user.organization.account.id)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_update_rate_plan_happy_path(
        self,
        create_rate_plan_api_factory,
        update_rate_plan,
        create_distributor_client_user,
    ):
        distributor_client_user = create_distributor_client_user()
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        rate_plan_id = response.json()["id"]
        response = update_rate_plan(rate_plan_id, accessFee=12.03)

        response_data = response.json()
        assert response.status_code == status.HTTP_200_OK
        assert response_data["accessFee"] == 12.03
        assert response_data["name"] == RATE_PLAN_REQUEST["name"]


class TestRatePlanAuth:
    def setup_method(self):
        self.rate_plan_service_mock = MagicMock(spec=AbstractRatePlanService)

    def test_rate_plans_by_account_as_distributor_staff(
        self,
        authenticate,
        api_factory,
        create_distributor_staff_user,
    ):
        distributor_staff_user = create_distributor_staff_user()
        authenticate(distributor_staff_user)
        with pytest.raises(Exception) as exc_info:
            api_factory("rate_plans_by_accounts")
        assert "" in str(exc_info.value)

    def test_rate_plans_by_account_as_distributor_client(
        self,
        authenticate,
        api_factory,
        create_distributor_client_user,
    ):
        distributor_client_user = create_distributor_client_user()
        authenticate(distributor_client_user)
        response = api_factory("rate_plans_by_accounts")
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_account_rate_plans_as_distributor_client(
        self,
        authenticate,
        api_factory,
        create_rate_plan_api_factory,
        create_distributor_client_user,
        create_distributor_staff_user,
    ):
        distributor_staff_user = create_distributor_staff_user()
        distributor_client_user = create_distributor_client_user()
        authenticate(distributor_staff_user)
        create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        authenticate(distributor_client_user)
        response = api_factory(
            "account_rate_plans",
            url_kwargs={"account_id": distributor_client_user.organization.id},
        )
        assert response.status_code == status.HTTP_200_OK

    def test_rate_plan_details_as_distributor_staff(
        self,
        authenticate,
        api_factory,
        create_rate_plan_api_factory,
        create_distributor_client_user,
        create_distributor_staff_user,
    ):
        distributor_staff_user = create_distributor_staff_user()
        distributor_client_user = create_distributor_client_user()

        authenticate(distributor_staff_user)
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        rate_plan_id = response.json()["id"]
        response = api_factory("rate_plan_details", url_kwargs={"id": rate_plan_id})
        assert response.status_code == status.HTTP_200_OK

    def test_rate_plan_details_as_distributor_client_with_permission(
        self,
        authenticate,
        api_factory,
        create_rate_plan_api_factory,
        create_distributor_client_user,
        create_distributor_staff_user,
    ):

        distributor_staff_user = create_distributor_staff_user()
        distributor_client_user = create_distributor_client_user()

        authenticate(distributor_staff_user)
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        rate_plan_id = response.json()["id"]

        authenticate(distributor_client_user)
        response = api_factory("rate_plan_details", url_kwargs={"id": rate_plan_id})
        assert response.status_code == status.HTTP_200_OK

    def test_rate_plan_details_distributor_client_without_permission(
        self,
        authenticate,
        api_factory,
        create_rate_plan_api_factory,
        create_distributor_client_user,
        create_distributor_staff_user,
    ):
        distributor_staff_user = create_distributor_staff_user()
        distributor_client_user_with_permission = create_distributor_client_user(
            organization_id=2, account_id=2
        )
        distributor_client_user_without_permission = create_distributor_client_user(
            organization_id=3, account_id=3
        )

        authenticate(distributor_staff_user)
        response = create_rate_plan_api_factory(
            distributor_client_user_with_permission.organization.id,
        )

        rate_plan_id = response.json()["id"]
        authenticate(distributor_client_user_without_permission)
        response = api_factory("rate_plan_details", url_kwargs={"id": rate_plan_id})
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_update_rate_plan_as_distributor_staff(
        self,
        authenticate,
        create_rate_plan_api_factory,
        update_rate_plan,
        create_distributor_client_user,
        create_distributor_staff_user,
    ):

        distributor_client_user = create_distributor_client_user()
        distributor_staff_user = create_distributor_staff_user()

        authenticate(distributor_staff_user)
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        rate_plan_id = response.json()["id"]
        response = update_rate_plan(rate_plan_id, accessFee=12.03)
        assert response.status_code == status.HTTP_200_OK

    @pytest.mark.skip("Need to change according to the new ratePlan")
    def test_update_rate_plan_as_distributor_client(
        self,
        authenticate,
        create_rate_plan_api_factory,
        update_rate_plan,
        create_distributor_client_user,
        create_distributor_staff_user,
    ):
        distributor_client_user = create_distributor_client_user()
        distributor_staff_user = create_distributor_staff_user()

        authenticate(distributor_staff_user)
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        rate_plan_id = response.json()["id"]
        authenticate(distributor_client_user)
        response = update_rate_plan(rate_plan_id, accessFee=1)
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_remove_rate_plan_as_distributor_staff(
        self,
        authenticate,
        api_factory,
        create_rate_plan_api_factory,
        create_distributor_client_user,
        create_distributor_staff_user,
    ):
        distributor_client_user = create_distributor_client_user()
        distributor_staff_user = create_distributor_staff_user()

        authenticate(distributor_staff_user)
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        rate_plan_id = response.json()["id"]
        response = api_factory(
            "remove_rate_plan", method="DELETE", url_kwargs={"id": rate_plan_id}
        )
        assert response.status_code == status.HTTP_202_ACCEPTED

    def test_remove_rate_plan_as_distributor_client(
        self,
        authenticate,
        api_factory,
        create_rate_plan_api_factory,
        create_distributor_client_user,
        create_distributor_staff_user,
    ):

        distributor_client_user = create_distributor_client_user()
        distributor_staff_user = create_distributor_staff_user()

        authenticate(distributor_staff_user)
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        rate_plan_id = response.json()["id"]
        authenticate(distributor_client_user)
        response = api_factory(
            "remove_rate_plan", method="DELETE", url_kwargs={"id": rate_plan_id}
        )
        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_create_rate_plan_as_distributor_staff(
        self,
        authenticate,
        create_rate_plan_api_factory,
        create_distributor_client_user,
        create_distributor_staff_user,
    ):
        distributor_staff_user = create_distributor_staff_user()
        distributor_client_user = create_distributor_client_user()

        authenticate(distributor_staff_user)
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        assert response.status_code == status.HTTP_201_CREATED

    @pytest.mark.skip("Need to change according to the new ratePlan")
    def test_create_rate_plan_as_distributor_client(
        self,
        authenticate,
        create_rate_plan_api_factory,
        create_distributor_client_user,
    ):
        distributor_client_user = create_distributor_client_user()

        authenticate(distributor_client_user)
        response = create_rate_plan_api_factory(
            distributor_client_user.organization.account.id,
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_get_rate_plans_models_success(self, mock_rate_plan_service):
        response = get_rate_plans_models(
            rate_plan_service=mock_rate_plan_service,
        )
        rate_models = list(
            map(
                lambda rate_model: schemas.RateModel(**rate_model), GET_RATE_PLAN_MODELS
            )
        )
        response_schema = schemas.RatePlanModels(result=rate_models)
        assert response.result == response_schema.result

    def test_get_rate_plans_models_not_found(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Rateplan models not found.",
        )

        with pytest.raises(HTTPException) as exc_info:
            get_rate_plans_models(
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Rateplan models not found." in exc_info.value.detail

    def test_get_rate_plans_models_exception(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=Exception,
            message="We couldn't process you request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            get_rate_plans_models(
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process you request." in exc_info.value.detail

    def test_get_rate_plans_model_by_id_success(self, mock_rate_plan_service):
        response = get_rate_plans_model_by_id(
            id=2,
            rate_plan_service=mock_rate_plan_service,
        )
        rate_models = list(
            map(
                lambda rate_model: schemas.RateModel(**rate_model), GET_RATE_PLAN_MODELS
            )
        )
        assert response == rate_models[1]
        assert isinstance(response, schemas.RateModel)
        assert response.value == 2
        assert response.title == "Individual Plan"

    def test_get_rate_plans_model_by_id_not_found(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Requested rateplan model not found.",
        )

        with pytest.raises(HTTPException) as exc_info:
            get_rate_plans_model_by_id(
                id=2,
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Requested rateplan model not found." in exc_info.value.detail

    def test_get_rate_plans_model_by_id_exception(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=Exception,
            message="We couldn't process you request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            get_rate_plans_model_by_id(
                id=2,
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process you request." in exc_info.value.detail

    def test_update_rate_plans_model_by_id_success(self, mock_rate_plan_service):
        rate_model = schemas.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)
        response = update_rate_plans_model_by_id(
            id=6,
            model=rate_model,
            rate_plan_service=mock_rate_plan_service,
        )
        assert isinstance(response, schemas.RateModel)
        assert response.value == 6
        assert response.title == "Individual Plan"

    def test_update_rate_plans_model_by_id_not_found(
        self, mock_rate_plan_error_service
    ):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Requested rateplan model not found.",
        )
        rate_model = schemas.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)

        with pytest.raises(HTTPException) as exc_info:
            update_rate_plans_model_by_id(
                id=6,
                model=rate_model,
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Requested rateplan model not found." in exc_info.value.detail

    def test_update_rate_plans_model_by_id_exception(
        self, mock_rate_plan_error_service
    ):
        mock_error_service = mock_rate_plan_error_service(
            error_type=Exception,
            message="We couldn't process you request.",
        )
        rate_model = schemas.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)

        with pytest.raises(HTTPException) as exc_info:
            update_rate_plans_model_by_id(
                id=6,
                model=rate_model,
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process you request." in exc_info.value.detail

    def test_create_rate_plan_model_success(self, mock_rate_plan_service):
        rate_model = schemas.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)
        response = create_rate_plan_model(
            model=rate_model,
            rate_plan_service=mock_rate_plan_service,
        )
        assert isinstance(response, schemas.RateModel)
        assert response.value == 6
        assert response.title == "Individual Plan"

    def test_create_rate_plan_model_not_found(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Requested rateplan model not found.",
        )
        rate_model = schemas.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)

        with pytest.raises(HTTPException) as exc_info:
            create_rate_plan_model(
                model=rate_model,
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Requested rateplan model not found." in exc_info.value.detail

    def test_create_rate_plan_model_exception(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=Exception,
            message="We couldn't process you request.",
        )
        rate_model = schemas.RatePlanModel(**CREATE_UPDATE_RATE_PLAN_MODEL)

        with pytest.raises(HTTPException) as exc_info:
            create_rate_plan_model(
                model=rate_model,
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process you request." in exc_info.value.detail

    def test_delete_rate_plan_model_success(self, mock_rate_plan_service):
        response = delete_rate_plan_model(
            id=1,
            rate_plan_service=mock_rate_plan_service,
        )
        expected_response = None
        assert response == expected_response

    def test_delete_rate_plan_model_not_found(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelNotFound,
            message="Requested rateplan model not found.",
        )

        with pytest.raises(HTTPException) as exc_info:
            delete_rate_plan_model(
                id=1,
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Requested rateplan model not found." in exc_info.value.detail

    def test_delete_rate_plan_model_exception(self, mock_rate_plan_error_service):
        mock_error_service = mock_rate_plan_error_service(
            error_type=Exception,
            message="We couldn't process you request.",
        )

        with pytest.raises(HTTPException) as exc_info:
            delete_rate_plan_model(
                id=1,
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "We couldn't process you request." in exc_info.value.detail

    def test_delete_rate_plan_model_rate_plan_delete(
        self, mock_rate_plan_error_service
    ):
        mock_error_service = mock_rate_plan_error_service(
            error_type=RatePlanModelDeletionError,
            message="Unable to delete rateplan model.",
        )

        with pytest.raises(HTTPException) as exc_info:
            delete_rate_plan_model(
                id=1,
                rate_plan_service=mock_error_service,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Unable to delete rateplan model." in exc_info.value.detail

    def test_rate_plans_by_accounts_success(self, mock_rate_plan_service, api_security):
        mock_auth, mock_request = api_security("/invoices/{id}")
        mock_request.method = "GET"
        ordering = Ordering(field="id", order="DESC")
        searching = Searching(search="account_name", fields=["account_name"])
        pagination = Pagination(page=1, page_size=1)

        response = rate_plans_by_accounts(
            request=mock_request,
            rate_plan_service=mock_rate_plan_service,
            authorization=mock_auth,
            ordering=ordering,
            searching=searching,
            pagination=pagination,
        )

        assert isinstance(response.results[0], schemas.AccountRatePlanDetails)
        assert response.results[0].account_name == "Tesla"

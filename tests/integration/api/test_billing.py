from copy import deepcopy
from datetime import datetime
from decimal import Decimal
from unittest.mock import <PERSON>Mock

from fastapi import HTTPException, status

from accounts.domain.model import AccountStatus, PaymentTerms, SalesChannel
from api.authorization.deps import authorization_service
from api.billing import deps, schemas
from api.billing.endpoints import (
    billing_cycle_report,
    create_adjustment,
    generate_billing_cycle_invoices,
    get_billing_cycle,
    get_invoices,
    get_invoices_new,
    invoice_details,
    invoice_reconciliation,
    invoice_usage,
    monthly_reconciliation,
    publish_invoice,
    remove_adjustment,
    remove_billing_cycle_invoices,
    unpublish_invoice,
    update_adjustment,
)
from api.billing.examples import (
    CREATE_INVOICE_ADJUSTMENT,
    RECONCILIATION,
    RECONCILIATION_RESPONSE,
    UPDATE_INVOICE_ADJUSTMENT,
)
from api.rate_plans.deps import get_rate_plan_repository
from api.rating.deps import _db_usage_repository
from api.sim.deps import _cdr_repository, _rate_plan_repository, get_sim_repository
from authorization.domain.ports import AbstractAuthorizationAPI
from authorization.services import FakeAuthorizationAPI
from billing.adapters.repository import (
    AbstractAccountRepository,
    AbstractBillingCycleRepository,
    InMemoryAccountRepository,
    InMemoryBillingCycleRepository,
)
from billing.domain import model
from billing.domain.model import Account, AdjustmentType, SubscriptionSIM
from billing.exceptions import (
    BillingCycleDoesNotExist,
    BillingObjectDoesNotExist,
    CalculationIncomplete,
    CalculationInProgress,
    InvoiceAlreadyPublished,
    InvoiceDoesNotExist,
    InvoiceHasNotBeenPublished,
)
from billing.services import AbstractBillingService, FakeBillingService, SimUsageService
from cdrdata.adapters.repository import AbstractCdrRepository, InMemoryCdrRepository
from common.types import Month, Service, SimStatus
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    InMemoryRatePlanRepository,
)
from rate_plans.domain.model import RatePlan
from rating.adapters.usage_repository import (
    AbstractUsageRepository,
    InMemoryUsageRepository,
)
from sim.adapters.repository import AbstractSimRepository, InMemorySimRepository

import pytest  # isort: skip


@pytest.fixture
def billing_cycle_repository() -> AbstractBillingCycleRepository:
    return InMemoryBillingCycleRepository()


@pytest.fixture
def rate_plan_repository() -> AbstractRatePlanRepository:
    return InMemoryRatePlanRepository([])


@pytest.fixture
def sim_repository() -> AbstractSimRepository:
    return InMemorySimRepository()


@pytest.fixture
def usage_repository() -> AbstractUsageRepository:
    return InMemoryUsageRepository()


@pytest.fixture
def account_repository() -> AbstractAccountRepository:
    account = Account(
        id=1,
        status=AccountStatus.ACTIVE,
        is_billable=True,
        sales_channel=SalesChannel.WHOLESALE,
        payment_terms=PaymentTerms(30),
        sim_charge=Decimal(0),
        contract_end_date=datetime.today(),
    )
    return InMemoryAccountRepository([account])


@pytest.fixture
def cdr_repository() -> AbstractCdrRepository:
    return InMemoryCdrRepository()


@pytest.fixture
def authorization() -> AbstractAuthorizationAPI:
    return FakeAuthorizationAPI()


@pytest.fixture
def fake_billing_service(client):
    overrides = client.app.dependency_overrides
    backup_deps = deepcopy(overrides)
    fake_billing_service = FakeBillingService()
    overrides[deps._get_billing_service] = lambda: fake_billing_service
    yield fake_billing_service
    client.app.dependency_overrides = backup_deps


@pytest.fixture(autouse=True)
def override_dependencies(
    client,
    account_repository,
    billing_cycle_repository,
    rate_plan_repository,
    sim_repository,
    usage_repository,
    cdr_repository,
    authorization,
):
    overrides = client.app.dependency_overrides
    backup_deps = deepcopy(overrides)

    overrides[deps._billing_cycle_repository] = lambda: billing_cycle_repository
    overrides[deps._rate_plan_repository] = lambda: rate_plan_repository
    overrides[get_sim_repository] = lambda: sim_repository
    overrides[_cdr_repository] = lambda: cdr_repository
    overrides[authorization_service] = lambda: authorization
    overrides[_rate_plan_repository] = lambda: rate_plan_repository

    overrides[_db_usage_repository] = lambda: usage_repository
    overrides[get_rate_plan_repository] = lambda: rate_plan_repository
    overrides[deps._sim_usage_service] = lambda: SimUsageService(
        usage_repository=usage_repository,
        billing_cycle_repository=billing_cycle_repository,
    )
    overrides[deps.account_resolver] = deps.fake_account_resolver
    overrides[deps.database_account_repository] = lambda: account_repository
    yield
    client.app.dependency_overrides = backup_deps


@pytest.fixture
def create_account():
    def factory(
        id: int,
        status: AccountStatus = AccountStatus.ACTIVE,
    ):
        return model.Account(
            id=id,
            status=status,
            is_billable=True,
            sales_channel=SalesChannel.WHOLESALE,
            payment_terms=PaymentTerms(30),
            sim_charge=Decimal(0),
            contract_end_date=datetime.today(),
        )

    return factory


@pytest.fixture
def create_invoice():
    def factory(account, id, with_usage=True, is_published=True):
        invoice = model.Invoice(account_id=account.id, due_days=30)
        invoice.account = account
        invoice.id = id
        invoice._adjustments = []
        invoice.billing_cycle = model.BillingCycle(month="2023-04")
        invoice.billing_cycle.insert_sim_usage(
            [
                model.SimUsage(
                    imsi=f"{1:0>15}",
                    service=s.value,
                    volume=1,
                    charge=1,
                )
                for s in Service
            ]
        )
        if is_published:
            invoice.published_at = datetime.now()
        if with_usage:
            invoice.usages = [
                model.ServiceUsage(
                    service=s,
                    volume=1,
                    bulk_overage_charge=Decimal(0),
                    sub_charge=Decimal(0),
                    total_overage_charge=Decimal(0),
                    charge=Decimal(1),
                )
                for s in Service
            ]
            subscription = model.Subscription(
                rate_plan_id=1,
                name="test",
                access_fee=Decimal(0.1),
                _sims_active=1,
                sims_total=1,
                sim_charge=Decimal(0),
                sims=[
                    SubscriptionSIM(
                        sim_id=1,
                        imsi="23344454545454",
                        iccid="987654321234567876",
                        msisdn="4454567654",
                        first_time_activated=True,
                        sim_status=SimStatus.ACTIVE,
                    )
                ],
            )
            invoice.subscriptions.append(subscription)
        return invoice

    return factory


class TestGenerateBillingCycleInvoices:
    def test_generate_billing_cycle_invoices(self, client, billing_cycle_repository):
        month_string = "2022-11"
        url = client.app.url_path_for(
            "generate_billing_cycle_invoices", month=month_string
        )
        response = client.post(url)

        assert response.status_code == status.HTTP_204_NO_CONTENT, response.content
        assert response.content == b""
        assert "x-trace-id" in response.headers

        billing_cycle = billing_cycle_repository.get(Month.from_str(month_string))

        assert billing_cycle is not None

    def test_generate_billing_cycle_invoices_when_calculation_in_progress(
        self, client, billing_cycle_repository
    ):
        month_string = "2022-11"
        billing_cycle = model.BillingCycle(Month.from_str(month_string))
        billing_cycle.make_invoice(22, PaymentTerms(20))
        billing_cycle_repository.add(billing_cycle)

        url = client.app.url_path_for(
            "generate_billing_cycle_invoices", month=month_string
        )
        response = client.post(url)

        assert response.status_code == status.HTTP_204_NO_CONTENT

    def test_invoice_for_account_created(
        self, client, billing_cycle_repository, rate_plan_repository, account_repository
    ):
        account, *_ = account_repository.query()
        rate_plan_repository.add(
            RatePlan(
                account_id=account.id,
                name="foo",
                access_fee=Decimal("0"),
                sim_limit=0,
                is_default=False,
            )
        )
        month_string = "2022-11"
        url = client.app.url_path_for(
            "generate_billing_cycle_invoices", month=month_string
        )
        response = client.post(url)

        assert response.status_code == status.HTTP_204_NO_CONTENT

        billing_cycle = billing_cycle_repository.get(Month.from_str(month_string))

        assert billing_cycle is not None
        invoice = billing_cycle.get_invoice(account.id)
        assert invoice is not None
        assert invoice.rating_state == model.InvoiceRatingState.GENERATED

    def test_invoice_for_account_with_closed_status(
        self, client, billing_cycle_repository, rate_plan_repository, account_repository
    ):
        account, *_ = account_repository.query()
        account.status = AccountStatus.CLOSED
        rate_plan_repository.add(
            RatePlan(
                account_id=account.id,
                name="foo",
                access_fee=Decimal("0"),
                sim_limit=0,
                is_default=False,
            )
        )
        month_string = "2022-11"
        url = client.app.url_path_for(
            "generate_billing_cycle_invoices", month=month_string
        )
        response = client.post(url)
        assert response.status_code == status.HTTP_204_NO_CONTENT
        billing_cycle = billing_cycle_repository.get(Month.from_str(month_string))
        assert billing_cycle.get_invoice(account.id) is None

    def test_invoice_due_date(
        self, client, billing_cycle_repository, rate_plan_repository, account_repository
    ):
        account, *_ = account_repository.query()
        account.payment_terms = PaymentTerms(42)
        month_string = "2022-11"
        url = client.app.url_path_for(
            "generate_billing_cycle_invoices", month=month_string
        )
        client.post(url)
        billing_cycle = billing_cycle_repository.get(Month.from_str(month_string))
        invoice = billing_cycle.get_invoice(account.id)
        invoice.publish()
        assert invoice.due_days == account.payment_terms


class TestRemoveBillingCycleInvoices:
    def test_happy_path_is_ok(self, client, billing_cycle_repository):
        month_string = "2022-11"
        billing_cycle = model.BillingCycle(Month.from_str(month_string))
        billing_cycle_repository.add(billing_cycle)

        url = client.app.url_path_for(
            "remove_billing_cycle_invoices", month=month_string
        )
        response = client.delete(url)

        assert response.status_code == status.HTTP_204_NO_CONTENT

    def test_remove_in_calculation_state(self, client, billing_cycle_repository):
        month_string = "2022-11"
        billing_cycle = model.BillingCycle(Month.from_str(month_string))
        billing_cycle.make_invoice(22, PaymentTerms(20))
        billing_cycle_repository.add(billing_cycle)

        url = client.app.url_path_for(
            "remove_billing_cycle_invoices", month=month_string
        )
        response = client.delete(url)

        assert response.status_code == status.HTTP_204_NO_CONTENT

    def test_remove_non_existed(self, client):
        url = client.app.url_path_for("remove_billing_cycle_invoices", month="2022-12")
        response = client.delete(url)
        assert response.status_code == status.HTTP_404_NOT_FOUND


class TestGetBillingCycle:
    def test_happy_path_is_ok(self, client, billing_cycle_repository):
        month_string = "2022-11"
        billing_cycle = model.BillingCycle(Month.from_str(month_string))
        billing_cycle.make_invoice(20, 30)
        billing_cycle_repository.add(billing_cycle)

        url = client.app.url_path_for("get_billing_cycle", month=month_string)
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.json()["month"] == month_string
        assert response.json()["invoices"][0]["account"]["id"] == 20

    def test_get_when_calculation_in_progress(self, client, billing_cycle_repository):
        month_string = "2022-11"
        billing_cycle = model.BillingCycle(Month.from_str(month_string))
        billing_cycle_repository.add(billing_cycle)

        url = client.app.url_path_for("get_billing_cycle", month=month_string)
        response = client.get(url)

        assert response.status_code == status.HTTP_200_OK


@pytest.fixture()
def make_invoice(billing_cycle_repository):
    def _factory(month: str = "2022-11", account_id: int = 20):
        bc = model.BillingCycle(Month.from_str(month))
        invoice = bc.make_invoice(account_id, PaymentTerms(30))
        invoice.rating_state = model.InvoiceRatingState.GENERATED
        billing_cycle_repository.add(bc)
        adjustment = model.Adjustment(
            id=None,
            date=datetime.now().date(),
            type=AdjustmentType.TRAINING,
            amount=Decimal(70.5),
        )
        invoice.add_adjustment(adjustment)
        billing_cycle_repository.update_invoice(invoice)
        return invoice

    return _factory


class TestInvoiceDetails:
    def test_happy_path_is_ok(self, client, make_invoice):
        invoice = make_invoice(month="2022-11", account_id=20)
        url = client.app.url_path_for("invoice_details", id=invoice.id)
        response = client.get(url)
        assert response.status_code == status.HTTP_200_OK
        assert response.json()["billingCycle"] == "2022-11"
        assert response.json()["account"]["id"] == 20


class TestRemoveAdjustment:
    def test_happy_path_is_ok(self, client, billing_cycle_repository, make_invoice):
        invoice = make_invoice()
        assert len(invoice.adjustments) == 1
        url = client.app.url_path_for(
            "remove_adjustment", invoice_id=invoice.id, id=invoice.adjustments[0].id
        )
        response = client.delete(url)
        assert response.status_code == 204
        invoice = billing_cycle_repository.get_invoice_by_id(invoice.id)
        assert len(invoice.adjustments) == 0

    def test_remove_from_non_existed_invoice(self, client, make_invoice):
        invoice = make_invoice()
        url = client.app.url_path_for(
            "remove_adjustment", invoice_id=invoice.id + 1, id=invoice.adjustments[0].id
        )
        response = client.delete(url)
        assert response.status_code == 404

    def test_remove_non_existed(self, client, make_invoice):
        invoice = make_invoice()
        non_existent_adjustment_id = max(invoice.adjustments, key=lambda a: a.id).id + 1
        url = client.app.url_path_for(
            "remove_adjustment", invoice_id=invoice.id, id=non_existent_adjustment_id
        )
        response = client.delete(url)
        assert response.status_code == 404


class TestUpdateAdjustment:
    def test_happy_path_is_ok(self, client, billing_cycle_repository, make_invoice):
        invoice = make_invoice()
        adjustment = invoice.adjustments[0]
        url = client.app.url_path_for(
            "update_adjustment", invoice_id=invoice.id, id=adjustment.id
        )
        new_amount = adjustment.amount + Decimal(10.5)
        request_body = {
            "id": adjustment.id,
            "date": str(datetime.now().date()),
            "type": AdjustmentType.TRAINING.name,
            "amount": float(new_amount),
        }

        response = client.put(url, json=request_body)
        assert response.status_code == 200
        assert request_body["amount"] == new_amount

        invoice = billing_cycle_repository.get_invoice_by_id(invoice.id)
        assert len(invoice.adjustments) == 1
        updated_adjustment = invoice.adjustments[0]
        assert updated_adjustment.amount == new_amount

    def test_id_changing_error(self, client, make_invoice):
        invoice = make_invoice()
        adjustment = invoice.adjustments[0]
        url = client.app.url_path_for(
            "update_adjustment", invoice_id=invoice.id, id=adjustment.id
        )
        request_body = {
            "id": adjustment.id + 1,
            "date": str(datetime.now().date()),
            "type": AdjustmentType.ACCOUNT_SETUP.name,
            "amount": float(adjustment.amount) + 10.5,
        }
        response = client.put(url, json=request_body)
        assert response.status_code == 400


class TestAuthenticationForBilling:
    def setup_method(self):
        self.billing_service_mock = MagicMock(spec=AbstractBillingService)

    @pytest.fixture(autouse=True)
    def authenticate_staff(self, authenticate, create_distributor_staff_user):
        authenticate(create_distributor_staff_user())

    @pytest.fixture
    def generate_billing_cycle(self, api_factory):
        def factory(month: str = "2023-03"):
            return api_factory(
                "generate_billing_cycle_invoices",
                method="POST",
                url_kwargs={"month": month},
            )

        return factory

    @pytest.fixture
    def generate_and_get_invoices(self, api_factory):
        def factory(month: str = "2023-03"):
            api_factory(
                "generate_billing_cycle_invoices",
                method="POST",
                url_kwargs={"month": month},
            )
            return api_factory("get_invoices")

        return factory

    # Test generate invoices
    def test_generate_bc_as_distributor_staff(
        self,
        api_factory,
        generate_billing_cycle,
    ):
        response = generate_billing_cycle()
        assert response.status_code == status.HTTP_204_NO_CONTENT

    def test_generate_bc_as_distributor_client(
        self,
        authenticate,
        api_factory,
        generate_billing_cycle,
        create_distributor_client_user,
    ):
        authenticate(create_distributor_client_user())
        response = generate_billing_cycle()
        assert response.status_code == status.HTTP_403_FORBIDDEN

    # Test get billing cycle
    def test_get_bc_as_distributor_staff(
        self,
        api_factory,
        generate_billing_cycle,
    ):
        generate_billing_cycle()
        response = api_factory("get_billing_cycle", url_kwargs={"month": "2023-03"})
        assert response.status_code == status.HTTP_200_OK

    def test_get_bc_as_distributor_client(
        self,
        authenticate,
        api_factory,
        generate_billing_cycle,
        create_distributor_client_user,
    ):
        generate_billing_cycle()
        authenticate(create_distributor_client_user())
        response = api_factory("get_billing_cycle", url_kwargs={"month": "2023-03"})
        assert response.status_code == status.HTTP_403_FORBIDDEN

    # Test delete billing cycle
    def test_delete_bc_as_distributor_staff(
        self,
        api_factory,
        generate_billing_cycle,
    ):
        generate_billing_cycle()
        response = api_factory(
            "remove_billing_cycle_invoices",
            url_kwargs={"month": "2023-03"},
            method="DELETE",
        )
        assert response.status_code == status.HTTP_204_NO_CONTENT

    def test_delete_bc_as_distributor_client(
        self,
        authenticate,
        api_factory,
        generate_billing_cycle,
        create_distributor_client_user,
    ):
        generate_billing_cycle()
        authenticate(create_distributor_client_user())
        response = api_factory(
            "remove_billing_cycle_invoices",
            url_kwargs={"month": "2023-03"},
            method="DELETE",
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    # Test generate report
    def test_generate_billing_cycle_report_as_distributor_staff(
        self,
        api_factory,
        generate_billing_cycle,
    ):
        generate_billing_cycle()
        response = api_factory("billing_cycle_report", url_kwargs={"month": "2023-03"})
        assert response.status_code == status.HTTP_200_OK

    def test_generate_billing_cycle_report_as_distributor_client(
        self,
        authenticate,
        api_factory,
        generate_billing_cycle,
        create_distributor_client_user,
    ):
        generate_billing_cycle()
        authenticate(create_distributor_client_user())
        response = api_factory("billing_cycle_report", url_kwargs={"month": "2023-03"})
        assert response.status_code == status.HTTP_403_FORBIDDEN

    # Test get invoices
    def test_get_invoices_as_distributor_staff(
        self,
        api_factory,
        generate_billing_cycle,
    ):
        generate_billing_cycle()
        response = api_factory("get_invoices")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) > 0

    def test_get_unpublished_invoices_as_distributor_client_with_permission(
        self,
        authenticate,
        api_factory,
        generate_billing_cycle,
        create_distributor_client_user,
    ):
        generate_billing_cycle()
        authenticate(create_distributor_client_user(account_id=20))
        response = api_factory("get_invoices")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 0

    def test_get_published_invoices_as_distributor_client_with_permission_without_usage(
        self,
        authenticate,
        api_factory,
        generate_and_get_invoices,
        create_distributor_client_user,
    ):
        response = generate_and_get_invoices()
        for invoice in response.json():
            api_factory(
                "publish_invoice", method="PUT", url_kwargs={"id": invoice["id"]}
            )

        authenticate(create_distributor_client_user(account_id=20))
        response = api_factory("get_invoices")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 0

    def test_get_published_invoices_as_distributor_client_with_permission_with_usage(
        self,
        authenticate,
        api_factory,
        create_distributor_client_user,
        create_invoice,
        fake_billing_service,
        create_account,
    ):
        distributor_client_user = create_distributor_client_user(account_id=20)
        invoice = create_invoice(
            create_account(
                id=distributor_client_user.organization.account.id,
            ),
            1,
            with_usage=True,
        )
        fake_billing_service.add_invoices([invoice])
        authenticate(distributor_client_user)
        response = api_factory("get_invoices")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) > 0

    def test_get_invoices_as_distributor_client_without_permission(
        self,
        authenticate,
        api_factory,
        generate_billing_cycle,
        create_distributor_client_user,
    ):
        generate_billing_cycle()
        authenticate(create_distributor_client_user())
        response = api_factory("get_invoices")
        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 0

    # Test get invoice details/usage
    @pytest.mark.parametrize("method_name", ["invoice_details", "invoice_usage"])
    def test_get_published_invoice_as_distributor_staff(
        self,
        api_factory,
        generate_and_get_invoices,
        method_name,
    ):
        invoices = generate_and_get_invoices().json()
        api_factory(
            "publish_invoice", method="PUT", url_kwargs={"id": invoices[0]["id"]}
        )

        response = api_factory(method_name, url_kwargs={"id": invoices[0]["id"]})
        assert response.status_code == status.HTTP_200_OK

    @pytest.mark.parametrize("method_name", ["invoice_details", "invoice_usage"])
    def test_get_unpublished_invoice_as_distributor_staff(
        self,
        api_factory,
        generate_and_get_invoices,
        method_name,
    ):
        invoices = generate_and_get_invoices().json()
        response = api_factory(method_name, url_kwargs={"id": invoices[0]["id"]})
        assert response.status_code == status.HTTP_200_OK

    @pytest.mark.parametrize(
        "method_name, account_status, response_code",
        [
            ("invoice_details", AccountStatus.ACTIVE, status.HTTP_200_OK),
            ("invoice_details", AccountStatus.SUSPENDED, status.HTTP_200_OK),
            ("invoice_details", AccountStatus.CLOSED, status.HTTP_404_NOT_FOUND),
            ("invoice_usage", AccountStatus.ACTIVE, status.HTTP_200_OK),
            ("invoice_usage", AccountStatus.SUSPENDED, status.HTTP_200_OK),
            ("invoice_usage", AccountStatus.CLOSED, status.HTTP_404_NOT_FOUND),
        ],
    )
    def test_get_published_invoice_as_distributor_client_with_permission_with_usage(
        self,
        authenticate,
        api_factory,
        create_distributor_client_user,
        method_name,
        account_status,
        response_code,
        create_invoice,
        fake_billing_service,
        create_account,
    ):
        distributor_client_user = create_distributor_client_user(account_id=20)
        invoice = create_invoice(
            create_account(
                id=distributor_client_user.organization.account.id,
                status=account_status,
            ),
            1,
            with_usage=True,
        )
        fake_billing_service.add_invoices([invoice])
        authenticate(distributor_client_user)
        response = api_factory(method_name, url_kwargs={"id": invoice.id})
        assert response.status_code == response_code

    @pytest.mark.parametrize("method_name", ["invoice_details", "invoice_usage"])
    def test_get_published_invoice_as_distributor_client_with_permission_without_usage(
        self,
        authenticate,
        api_factory,
        generate_and_get_invoices,
        create_distributor_client_user,
        method_name,
    ):
        distributor_client_user = create_distributor_client_user(account_id=20)
        invoices = generate_and_get_invoices().json()
        api_factory(
            "publish_invoice", method="PUT", url_kwargs={"id": invoices[0]["id"]}
        )

        authenticate(distributor_client_user)
        response = api_factory(method_name, url_kwargs={"id": invoices[0]["id"]})
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.parametrize("method_name", ["invoice_details", "invoice_usage"])
    def test_get_published_invoice_as_distributor_client_without_permission(
        self,
        authenticate,
        api_factory,
        generate_and_get_invoices,
        create_distributor_client_user,
        method_name,
    ):
        invoices = generate_and_get_invoices().json()
        api_factory(
            "publish_invoice", method="PUT", url_kwargs={"id": invoices[0]["id"]}
        )

        authenticate(create_distributor_client_user())
        response = api_factory(method_name, url_kwargs={"id": invoices[0]["id"]})
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.parametrize("method_name", ["invoice_details", "invoice_usage"])
    def test_get_unpublished_invoice_as_distributor_client_without_permission(
        self,
        authenticate,
        api_factory,
        generate_and_get_invoices,
        create_distributor_client_user,
        method_name,
    ):
        invoices = generate_and_get_invoices().json()
        authenticate(create_distributor_client_user(account_id=20))
        response = api_factory(method_name, url_kwargs={"id": invoices[0]["id"]})
        assert response.status_code == status.HTTP_404_NOT_FOUND

    # Test publish invoice
    def test_publish_invoice_as_distributor_staff(
        self,
        api_factory,
        generate_and_get_invoices,
        create_distributor_staff_user,
    ):
        invoices = generate_and_get_invoices().json()
        response = api_factory(
            "publish_invoice", method="PUT", url_kwargs={"id": invoices[0]["id"]}
        )
        assert response.status_code == status.HTTP_200_OK

    def test_publish_invoice_as_distributor_client(
        self,
        authenticate,
        api_factory,
        generate_and_get_invoices,
        create_distributor_client_user,
    ):
        invoices = generate_and_get_invoices().json()

        authenticate(create_distributor_client_user(account_id=20))
        response = api_factory(
            "publish_invoice", method="PUT", url_kwargs={"id": invoices[0]["id"]}
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    # Test unpublish invoice
    def test_unpublish_invoice_as_distributor_staff(
        self,
        api_factory,
        generate_and_get_invoices,
    ):
        invoices = generate_and_get_invoices().json()
        api_factory(
            "publish_invoice", method="PUT", url_kwargs={"id": invoices[0]["id"]}
        )

        response = api_factory(
            "unpublish_invoice", method="DELETE", url_kwargs={"id": invoices[0]["id"]}
        )
        assert response.status_code == status.HTTP_204_NO_CONTENT

    def test_unpublish_invoice_as_distributor_client(
        self,
        authenticate,
        api_factory,
        generate_and_get_invoices,
        create_distributor_client_user,
    ):
        invoices = generate_and_get_invoices().json()
        api_factory(
            "publish_invoice", method="PUT", url_kwargs={"id": invoices[0]["id"]}
        )

        authenticate(create_distributor_client_user(account_id=20))
        response = api_factory(
            "unpublish_invoice", method="DELETE", url_kwargs={"id": invoices[0]["id"]}
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    # Test getting adjustment types
    @pytest.mark.parametrize(
        "distributor_fixture",
        ["create_distributor_staff_user", "create_distributor_staff_user"],
    )
    def test_get_adjustment_types_as_distributor(
        self,
        authenticate,
        api_factory,
        request,
        distributor_fixture,
    ):
        distributor = request.getfixturevalue(distributor_fixture)
        authenticate(distributor())
        response = api_factory("adjustment_types")
        assert response.status_code == status.HTTP_200_OK

    # Test create adjustment
    def test_create_adjustment_as_distributor_staff(
        self,
        api_factory,
        generate_and_get_invoices,
    ):
        invoices = generate_and_get_invoices().json()
        response = api_factory(
            "create_adjustment",
            method="POST",
            url_kwargs={"id": invoices[0]["id"]},
            request_kwargs={"json": CREATE_INVOICE_ADJUSTMENT},
        )
        assert response.status_code == status.HTTP_201_CREATED

    def test_create_adjustment_as_distributor_client(
        self,
        authenticate,
        api_factory,
        generate_and_get_invoices,
        create_distributor_client_user,
    ):
        invoices = generate_and_get_invoices().json()
        authenticate(create_distributor_client_user(account_id=20))
        response = api_factory(
            "create_adjustment",
            method="POST",
            url_kwargs={"id": invoices[0]["id"]},
            request_kwargs={"json": CREATE_INVOICE_ADJUSTMENT},
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    # Test update adjustment
    def test_update_adjustment_as_distributor_staff(
        self,
        api_factory,
        generate_and_get_invoices,
    ):
        invoices = generate_and_get_invoices().json()
        adjustment_response = api_factory(
            "create_adjustment",
            method="POST",
            url_kwargs={"id": invoices[0]["id"]},
            request_kwargs={"json": CREATE_INVOICE_ADJUSTMENT},
        )
        adjustment = adjustment_response.json()

        response = api_factory(
            "update_adjustment",
            method="PUT",
            url_kwargs={"invoice_id": invoices[0]["id"], "id": adjustment["id"]},
            request_kwargs={
                "json": {**UPDATE_INVOICE_ADJUSTMENT, "id": adjustment["id"]}
            },
        )
        assert response.status_code == status.HTTP_200_OK

    def test_update_adjustment_as_distributor_client(
        self,
        authenticate,
        api_factory,
        generate_and_get_invoices,
        create_distributor_client_user,
    ):
        invoices = generate_and_get_invoices().json()
        adjustment_response = api_factory(
            "create_adjustment",
            method="POST",
            url_kwargs={"id": invoices[0]["id"]},
            request_kwargs={"json": CREATE_INVOICE_ADJUSTMENT},
        )
        adjustment = adjustment_response.json()

        authenticate(create_distributor_client_user(account_id=20))
        response = api_factory(
            "update_adjustment",
            method="PUT",
            url_kwargs={"invoice_id": invoices[0]["id"], "id": adjustment["id"]},
            request_kwargs={
                "json": {**UPDATE_INVOICE_ADJUSTMENT, "id": adjustment["id"]}
            },
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    # Test remove adjustment
    def test_delete_adjustment_as_distributor_staff(
        self,
        api_factory,
        generate_and_get_invoices,
    ):
        invoices = generate_and_get_invoices().json()
        adjustment_response = api_factory(
            "create_adjustment",
            method="POST",
            url_kwargs={"id": invoices[0]["id"]},
            request_kwargs={"json": CREATE_INVOICE_ADJUSTMENT},
        )
        adjustment = adjustment_response.json()

        response = api_factory(
            "remove_adjustment",
            method="DELETE",
            url_kwargs={"invoice_id": invoices[0]["id"], "id": adjustment["id"]},
        )
        assert response.status_code == status.HTTP_204_NO_CONTENT

    def test_delete_adjustment_as_distributor_client(
        self,
        authenticate,
        api_factory,
        generate_and_get_invoices,
        create_distributor_client_user,
    ):
        invoices = generate_and_get_invoices().json()
        adjustment_response = api_factory(
            "create_adjustment",
            method="POST",
            url_kwargs={"id": invoices[0]["id"]},
            request_kwargs={"json": CREATE_INVOICE_ADJUSTMENT},
        )
        adjustment = adjustment_response.json()

        authenticate(create_distributor_client_user(account_id=20))
        response = api_factory(
            "remove_adjustment",
            method="DELETE",
            url_kwargs={"invoice_id": invoices[0]["id"], "id": adjustment["id"]},
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_invoice_reconciliation_success(self):
        month = Month(2023, 5, 1)
        billing_service_mock = MagicMock(spec=AbstractBillingService)
        billing_service_mock.invoice_reconciliation.return_value = RECONCILIATION
        expected_response = schemas.Reconciliation(**RECONCILIATION_RESPONSE)

        response = invoice_reconciliation(
            month=month, billing_service=billing_service_mock
        )

        assert response == expected_response
        billing_service_mock.invoice_reconciliation.assert_called_once_with(month)
        assert isinstance(response, schemas.Reconciliation)

    def test_invoice_reconciliation_success_all_match(self):
        month = Month(2023, 5, 1)
        billing_service_mock = MagicMock(spec=AbstractBillingService)
        success_all_match_msg = "Success: all the data matched."
        billing_service_mock.invoice_reconciliation.return_value = success_all_match_msg
        expected_response = schemas.Reconciliation(result=success_all_match_msg)

        response = invoice_reconciliation(
            month=month, billing_service=billing_service_mock
        )

        assert response == expected_response
        billing_service_mock.invoice_reconciliation.assert_called_once_with(month)
        assert isinstance(response, schemas.Reconciliation)

    def test_invoice_reconciliation_error(self):
        month = Month(2023, 5, 1)
        billing_service_mock = MagicMock(spec=AbstractBillingService)
        billing_service_mock.invoice_reconciliation.return_value = RECONCILIATION
        billing_service_mock.invoice_reconciliation.side_effect = (
            BillingCycleDoesNotExist()
        )

        with pytest.raises(HTTPException) as exc_info:
            invoice_reconciliation(month=month, billing_service=billing_service_mock)
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Billing cycle does not exist."

    def test_get_monthly_reconciliation_success(self):
        month = Month(2023, 5, 1)
        billing_service_mock = MagicMock(spec=AbstractBillingService)
        billing_service_mock.get_monthly_reconciliation.return_value = RECONCILIATION
        expected_response = schemas.Reconciliation(**RECONCILIATION_RESPONSE)

        response = monthly_reconciliation(
            month=month, billing_service=billing_service_mock
        )

        assert response == expected_response
        billing_service_mock.get_monthly_reconciliation.assert_called_once_with(month)
        assert isinstance(response, schemas.Reconciliation)

    def test_get_monthly_reconciliation_success_all_match(self):
        month = Month(2023, 5, 1)
        billing_service_mock = MagicMock(spec=AbstractBillingService)
        success_all_match_msg = "Success: all the data matched."
        billing_service_mock.get_monthly_reconciliation.return_value = (
            success_all_match_msg
        )
        expected_response = schemas.Reconciliation(result=success_all_match_msg)

        response = monthly_reconciliation(
            month=month, billing_service=billing_service_mock
        )

        assert response == expected_response
        billing_service_mock.get_monthly_reconciliation.assert_called_once_with(month)
        assert isinstance(response, schemas.Reconciliation)

    def test_get_monthly_reconciliation_error(self):
        month = Month(2023, 5, 1)
        billing_service_mock = MagicMock(spec=AbstractBillingService)
        billing_service_mock.get_monthly_reconciliation.return_value = RECONCILIATION
        billing_service_mock.get_monthly_reconciliation.side_effect = (
            BillingCycleDoesNotExist()
        )

        with pytest.raises(HTTPException) as exc_info:
            monthly_reconciliation(month=month, billing_service=billing_service_mock)
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Billing cycle does not exist."

    def test_invoice_details_not_found(self, api_security):
        invoice_id = 1
        mock_auth, mock_request = api_security("/invoices/{id}")
        mock_request.method = "GET"
        self.billing_service_mock.get_invoice.side_effect = InvoiceDoesNotExist(
            invoice_id=invoice_id
        )
        with pytest.raises(HTTPException) as exc_info:
            invoice_details(
                request=mock_request,
                invoice_id=invoice_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_publish_invoice_not_found(self, api_security):
        invoice_id = 1
        mock_auth, mock_request = api_security("/invoices/{id}/published")
        mock_request.method = "PUT"
        self.billing_service_mock.publish_invoice.side_effect = InvoiceDoesNotExist(
            invoice_id=invoice_id
        )
        with pytest.raises(HTTPException) as exc_info:
            publish_invoice(
                request=mock_request,
                invoice_id=invoice_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_publish_invoice_calculation_incomplete(self, api_security):
        invoice_id = 1
        mock_auth, mock_request = api_security("/invoices/{id}/published")
        mock_request.method = "PUT"
        self.billing_service_mock.publish_invoice.side_effect = CalculationIncomplete()
        with pytest.raises(HTTPException) as exc_info:
            publish_invoice(
                request=mock_request,
                invoice_id=invoice_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == ""

    def test_publish_invoice_invoice_already_published(self, api_security):
        invoice_id = 1
        mock_auth, mock_request = api_security("/invoices/{id}/published")
        mock_request.method = "PUT"
        self.billing_service_mock.publish_invoice.side_effect = (
            InvoiceAlreadyPublished()
        )
        with pytest.raises(HTTPException) as exc_info:
            publish_invoice(
                request=mock_request,
                invoice_id=invoice_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "The invoice has already been published."

    def test_unpublish_invoice_not_found(self, api_security):
        invoice_id = 1
        self.billing_service_mock.unpublish_invoice.side_effect = InvoiceDoesNotExist(
            invoice_id=invoice_id
        )
        _, mock_request = api_security("/invoices/{id}/published")
        mock_request.method = "DELETE"
        with pytest.raises(HTTPException) as exc_info:
            unpublish_invoice(
                request=mock_request,
                invoice_id=invoice_id,
                billing_service=self.billing_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_unpublish_invoice_calculation_incomplete(self, api_security):
        invoice_id = 1
        self.billing_service_mock.unpublish_invoice.side_effect = (
            CalculationIncomplete()
        )
        _, mock_request = api_security("/invoices/{id}/published")
        mock_request.method = "DELETE"
        with pytest.raises(HTTPException) as exc_info:
            unpublish_invoice(
                request=mock_request,
                invoice_id=invoice_id,
                billing_service=self.billing_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == ""

    def test_unpublish_invoice_invoice_already_published(self, api_security):
        invoice_id = 1
        self.billing_service_mock.unpublish_invoice.side_effect = (
            InvoiceHasNotBeenPublished()
        )
        _, mock_request = api_security("/invoices/{id}/published")
        mock_request.method = "DELETE"
        with pytest.raises(HTTPException) as exc_info:
            unpublish_invoice(
                request=mock_request,
                invoice_id=invoice_id,
                billing_service=self.billing_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "The invoice has not been published yet."

    def test_invoice_usage_not_found(self, api_security):
        invoice_id = 1
        mock_auth, mock_request = api_security("/invoices/{id}/usage")
        mock_request.method = "GET"
        self.billing_service_mock.get_invoice.side_effect = InvoiceDoesNotExist(
            invoice_id=invoice_id
        )
        with pytest.raises(HTTPException) as exc_info:
            invoice_usage(
                request=mock_request,
                invoice_id=invoice_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_create_adjustment_not_found(self, api_security):
        invoice_id = 1
        create_adjustment_request = schemas.CreateAdjustment(
            **CREATE_INVOICE_ADJUSTMENT
        )
        mock_auth, mock_request = api_security("/invoices/{id}/adjustments")
        mock_request.method = "POST"
        self.billing_service_mock.add_invoice_adjustment.side_effect = (
            InvoiceDoesNotExist(invoice_id=invoice_id)
        )
        with pytest.raises(HTTPException) as exc_info:
            create_adjustment(
                request=mock_request,
                invoice_id=invoice_id,
                adjustment_input=create_adjustment_request,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_create_adjustment_calculation_incomplete(self, api_security):
        invoice_id = 1
        create_adjustment_request = schemas.CreateAdjustment(
            **CREATE_INVOICE_ADJUSTMENT
        )
        mock_auth, mock_request = api_security("/invoices/{id}/adjustments")
        mock_request.method = "POST"
        self.billing_service_mock.add_invoice_adjustment.side_effect = (
            CalculationIncomplete()
        )
        with pytest.raises(HTTPException) as exc_info:
            create_adjustment(
                request=mock_request,
                invoice_id=invoice_id,
                adjustment_input=create_adjustment_request,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == ""

    def test_create_adjustment_invoice_already_published(self, api_security):
        invoice_id = 1
        create_adjustment_request = schemas.CreateAdjustment(
            **CREATE_INVOICE_ADJUSTMENT
        )
        mock_auth, mock_request = api_security("/invoices/{id}/adjustments")
        mock_request.method = "POST"
        self.billing_service_mock.add_invoice_adjustment.side_effect = (
            InvoiceAlreadyPublished()
        )
        with pytest.raises(HTTPException) as exc_info:
            create_adjustment(
                request=mock_request,
                invoice_id=invoice_id,
                adjustment_input=create_adjustment_request,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "The invoice has already been published."

    def test_remove_adjustment_calculation_incomplete(self, api_security):
        invoice_id = 1
        adjustment_id = 1
        mock_auth, mock_request = api_security("/invoices/{id}/adjustments")
        mock_request.method = "POST"
        self.billing_service_mock.remove_invoice_adjustment.side_effect = (
            CalculationIncomplete()
        )
        with pytest.raises(HTTPException) as exc_info:
            remove_adjustment(
                request=mock_request,
                invoice_id=invoice_id,
                adjustment_id=adjustment_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == ""

    def test_remove_adjustment_invoice_already_published(self, api_security):
        invoice_id = 1
        adjustment_id = 1
        mock_auth, mock_request = api_security("/invoices/{id}/adjustments")
        mock_request.method = "POST"
        self.billing_service_mock.remove_invoice_adjustment.side_effect = (
            InvoiceAlreadyPublished()
        )
        with pytest.raises(HTTPException) as exc_info:
            remove_adjustment(
                request=mock_request,
                invoice_id=invoice_id,
                adjustment_id=adjustment_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "The invoice has already been published."

    def test_update_adjustment_billing_object_error(self, api_security):
        invoice_id = 1
        adjustment_id = 101
        adjustment_input = schemas.UpdateAdjustment(**UPDATE_INVOICE_ADJUSTMENT)
        mock_auth, mock_request = api_security(
            "/invoices/{invoice_id}/adjustments/{id}"
        )
        mock_request.method = "POST"
        self.billing_service_mock.update_invoice_adjustment.side_effect = (
            BillingObjectDoesNotExist()
        )
        with pytest.raises(HTTPException) as exc_info:
            update_adjustment(
                request=mock_request,
                adjustment_input=adjustment_input,
                invoice_id=invoice_id,
                adjustment_id=adjustment_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == ""

    def test_update_adjustment_calculation_incomplete(self, api_security):
        invoice_id = 1
        adjustment_id = 101
        adjustment_input = schemas.UpdateAdjustment(**UPDATE_INVOICE_ADJUSTMENT)
        mock_auth, mock_request = api_security(
            "/invoices/{invoice_id}/adjustments/{id}"
        )
        mock_request.method = "POST"
        self.billing_service_mock.update_invoice_adjustment.side_effect = (
            CalculationIncomplete()
        )
        with pytest.raises(HTTPException) as exc_info:
            update_adjustment(
                request=mock_request,
                adjustment_input=adjustment_input,
                invoice_id=invoice_id,
                adjustment_id=adjustment_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == ""

    def test_update_adjustment_invoice_already_published(self, api_security):
        invoice_id = 1
        adjustment_id = 101
        adjustment_input = schemas.UpdateAdjustment(**UPDATE_INVOICE_ADJUSTMENT)
        mock_auth, mock_request = api_security(
            "/invoices/{invoice_id}/adjustments/{id}"
        )
        mock_request.method = "POST"
        self.billing_service_mock.update_invoice_adjustment.side_effect = (
            InvoiceAlreadyPublished()
        )
        with pytest.raises(HTTPException) as exc_info:
            update_adjustment(
                request=mock_request,
                adjustment_input=adjustment_input,
                invoice_id=invoice_id,
                adjustment_id=adjustment_id,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "The invoice has already been published."

    def test_get_invoices_bill_cycle_does_not_exist(self, api_security):
        month = "2024-09"
        mock_auth, mock_request = api_security("/invoices-old")
        mock_request.method = "GET"
        self.billing_service_mock.get_invoices.side_effect = BillingCycleDoesNotExist()
        with pytest.raises(HTTPException) as exc_info:
            get_invoices(
                request=mock_request,
                month=month,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_get_billing_cycle_bill_cycle_does_not_exist(self):
        month = "2024-09"
        self.billing_service_mock.get_invoices.side_effect = BillingCycleDoesNotExist()
        with pytest.raises(HTTPException) as exc_info:
            get_billing_cycle(
                month=month,
                billing_service=self.billing_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_generate_billing_cycle_invoices_calculation_error(self, api_security):
        month = "2024-09"
        account_id = 1
        mock_auth, mock_request = api_security("/cycles/{month}")
        mock_request.method = "POST"
        self.billing_service_mock.generate_invoices.side_effect = (
            CalculationInProgress()
        )
        with pytest.raises(HTTPException) as exc_info:
            generate_billing_cycle_invoices(
                request=mock_request,
                month=month,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
                account_id=account_id,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "Calculation in progress."

    def test_remove_billing_cycle_invoices_calculation_error(self):
        month = "2024-09"
        self.billing_service_mock.remove_invoices.side_effect = CalculationInProgress()
        with pytest.raises(HTTPException) as exc_info:
            remove_billing_cycle_invoices(
                month=month,
                billing_service=self.billing_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "Calculation in progress."

    def test_billing_cycle_report_does_not_exist(self):
        month = "2024-09"
        self.billing_service_mock.get_invoices.side_effect = BillingCycleDoesNotExist()
        with pytest.raises(HTTPException) as exc_info:
            billing_cycle_report(
                month=month,
                billing_service=self.billing_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_get_invoices_error(self, api_security):
        month = "2024-09"
        mock_auth, mock_request = api_security("/invoices-old")
        mock_request.method = "GET"
        self.billing_service_mock.get_invoices.side_effect = Exception()
        with pytest.raises(HTTPException) as exc_info:
            get_invoices(
                request=mock_request,
                month=month,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "We couldn't process your request."

    def test_get_invoices_new_bill_cycle_does_not_exist(self, api_security):
        month = "2024-09"
        mock_auth, mock_request = api_security("/invoices")
        mock_request.method = "GET"
        self.billing_service_mock.get_invoices_new.side_effect = (
            BillingCycleDoesNotExist()
        )
        with pytest.raises(HTTPException) as exc_info:
            get_invoices_new(
                request=mock_request,
                month=month,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_get_invoices_new_error(self, api_security):
        month = "2024-09"
        mock_auth, mock_request = api_security("/invoices")
        mock_request.method = "GET"
        self.billing_service_mock.get_invoices_new.side_effect = Exception()
        with pytest.raises(HTTPException) as exc_info:
            get_invoices_new(
                request=mock_request,
                month=month,
                billing_service=self.billing_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "We couldn't process your request."

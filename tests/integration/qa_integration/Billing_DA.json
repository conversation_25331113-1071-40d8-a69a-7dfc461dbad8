{"info": {"_postman_id": "835dbf40-50a5-4bfd-9b36-02b10da73717", "name": "Billing_DA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29298941", "_collection_link": "https://nextgenclearing.postman.co/workspace/QA_SIMplify_Team~82bc3228-fd80-437d-bfc3-fd6585e93620/collection/29298941-835dbf40-50a5-4bfd-9b36-02b10da73717?action=share&source=collection_link&creator=29298941"}, "item": [{"name": "Generate Billing Cycle", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Created All Account Billing - 204 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}?account_id={{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": "{{account_id}}"}]}}, "response": []}, {"name": "Created All Account Billing - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/ccles/{{Billing_cycle_date}}?account_id={{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "ccles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": "{{account_id}}"}]}}, "response": []}, {"name": "Created All Account Billing - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}?account_id={{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": "{{account_id}}"}]}}, "response": []}, {"name": "Created All Account Billing - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}?account_id={{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": "{{account_id}}"}]}}, "response": []}, {"name": "Created All Account Billing - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}?account_id={{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": "{{account_id}}"}]}}, "response": []}, {"name": "Create Account - Create Account (Valid Name)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set(\"AccountId\", jsonData.id);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Name', randomString(10));\r", "\r", "var moment = require('moment');\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('contractEndDate', enddate.format((\"YYYY-MM-DD\")));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{Name}}\",\r\n  \"agreementNumber\": \"************\",\r\n  \"logoKey\": null,\r\n  \"status\": \"ACTIVE\",\r\n  \"salesChannel\": \"WHOLESALE\",\r\n  \"salesPerson\": \"QA_Test\",\r\n  \"industryVertical\": \"AEROSPACE\",\r\n  \"contractEndDate\": \"{{contractEndDate}}\",\r\n  \"isBillable\": true,\r\n  \"contactName\": \"uma\",\r\n  \"email\": \"{{Username}}\",\r\n  \"phone\": \"8600660247854\",\r\n  \"jobTitle\": \"QA\",\r\n  \"country\": \"UK\",\r\n  \"stateRegion\": \"aaaaa\",\r\n  \"city\": \"bbbbb\",\r\n  \"address1\": \"abc123\",\r\n  \"address2\": \"xyzaas\",\r\n  \"postcode\": \"wsdddd\",\r\n  \"thresholdCharge\": 0,\r\n  \"warningThreshold\": 0,\r\n  \"simCharge\": 0,\r\n  \"paymentTerms\": 99,\r\n  \"currency\": \"GBR\",\r\n  \"simProfile\": \"DATA_ONLY\",\r\n  \"productTypes\": [\r\n    \"NATIONAL_ROAMING\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts"]}}, "response": []}, {"name": "Generate Billing Cycle - <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}?account_id={{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": "{{AccountId}}"}]}}, "response": []}, {"name": "Generate Billing Cycle - Duplicate Case", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}?account_id={{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": "{{AccountId}}"}]}}, "response": []}, {"name": "Generate Billing Cycle  - Invalid Date Format", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/2024?account_id={{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "2024"], "query": [{"key": "account_id", "value": "{{AccountId}}"}]}}, "response": []}, {"name": "Generate Billing Cycle - Invalid Year", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/24-08?account_id={{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "24-08"], "query": [{"key": "account_id", "value": "{{AccountId}}"}]}}, "response": []}, {"name": "Generate Billing Cycle - Invalid Month", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/2024-14?account_id={{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "2024-14"], "query": [{"key": "account_id", "value": "{{AccountId}}"}]}}, "response": []}, {"name": "Generate Billing Cycle - Blank Year", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/-08?account_id={{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "-08"], "query": [{"key": "account_id", "value": "{{AccountId}}"}]}}, "response": []}, {"name": "Generate Billing Cycle - Blank Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}?account_id=", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": ""}]}}, "response": []}, {"name": "Generate Billing Cycle - Invalid Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}?account_id=invalid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": "invalid"}]}}, "response": []}, {"name": "Generate Billing Cycle - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}?account_id={{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}"], "query": [{"key": "account_id", "value": "{{AccountId}}"}]}}, "response": []}]}, {"name": "Get Invoices New", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get invoices - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test('Received id', function() {\r", "    const data = pm.response.json();\r", "    const m1 = pm.environment.get('AccountId');\r", "  for (let i = 0; i < data.length; i++) {\r", "        pm.environment.set('invoice_id', data[i]['id']);\r", "    }\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const previousMonth = moment().subtract(1, 'months').startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', previousMonth);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}]}}, "response": []}, {"name": "Get invoices - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoics?month={{Billing_cycle_date}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoics"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}]}}, "response": []}, {"name": "Get invoices - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}]}}, "response": []}, {"name": "Get invoices - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}]}}, "response": []}, {"name": "Get invoices - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}]}}, "response": []}, {"name": "Get invoices - Invalid Year Format", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month=23-09", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "23-09"}]}}, "response": []}, {"name": "Get invoices - Blank Month", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month=", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": ""}]}}, "response": []}, {"name": "Get invoices - Invalid Month", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month=2023-122", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "2023-122"}]}}, "response": []}, {"name": "Get invoices - Without Month", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "2023-122", "disabled": true}]}}, "response": []}]}, {"name": "Invoice Details", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Invoice Details - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoics/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoics", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Blank Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ '", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " '"]}}, "response": []}, {"name": "Invoice Details - Invalid Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/123b '", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "123b '"]}}, "response": []}, {"name": "invoice details - Invoice ID (Out Of Boundary)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/3235654551", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "3235654551"]}}, "response": []}]}, {"name": "Create Adjustment", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create adjustments - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "         \r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set(\"Adjustment_id\", jsonData.id);"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('Adjustment date', firstdate);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('Adjustment date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoics/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoics", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('Adjustment date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('Adjustment date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "         \r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('Adjustment date', firstdate);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Invalid Year Format", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"23-09-1\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Invalid Month Format", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-Sep-1\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Invalid Date Format", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"01-09-2023\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Blank Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": ,\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Without Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Invalid Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-1\",\r\n  \"type\": \"OTHER\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Blank Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-1\",\r\n  \"type\": ,\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Without Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-1\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - <PERSON><PERSON> Amount Field", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-1\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": \r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - <PERSON><PERSON><PERSON> Amount", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-1\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": ac11\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Without Amount field", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-1\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\"\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Invalid invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('Adjustment date', firstdate);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/r4f65132/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "r4f65132", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Blank Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('Adjustment date', firstdate);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 100\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices//adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "", "adjustments"]}}, "response": []}, {"name": "Create adjustments - Without Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "         \r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('Adjustment date', firstdate);"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\"\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/5.234/adjustments", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "5.234", "adjustments"]}}, "response": []}]}, {"name": "Update Adjustments", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Update adjustments - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 350,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/ {{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", " {{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 350,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/ {{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", " {{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 50,\r\n  \"id\": 115\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustmentssdgxc/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustmentssdgxc", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 50,\r\n  \"id\": 115\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Invalid ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 50,\r\n  \"id\": d5f41\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 350,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/ {{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", " {{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Blank Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 50,\r\n  \"id\": 115\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices//adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Invalid Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 50,\r\n  \"id\": 115\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/-115/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "-115", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Invalid Year", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n \"date\": \"23-09-1\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 550,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Invalid Month", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\":\"2023-Sep-1\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 550,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Invalid Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n \"date\": \"01-09-2023\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 550,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Blank Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": ,\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 550,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Without Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 550,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Invalid Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-12\",\r\n  \"type\": \"dfcasad\",\r\n  \"amount\": 550,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Blank Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-12\",\r\n  \"type\": ,\r\n  \"amount\": 550,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Without Type Field", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-12\",\r\n  \"amount\": 550,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - B<PERSON> Amount Field", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-12\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": ,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Invalid Amount Field", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-12\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": ,\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Without Amount Field", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"2023-09-12\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"id\": {{Adjustment_id}}\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Blank ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 50,\r\n  \"id\": \r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - Without ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 50\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Update adjustments - ID Decimal", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"date\": \"{{Adjustment date}}\",\r\n  \"type\": \"OTHER_CHARGE_ONE_TIME\",\r\n  \"amount\": 50,\r\n  \"id\": 22.25\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "{{Adjustment_id}}"]}}, "response": []}]}, {"name": "Remove Adjustment", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Remove adjustments - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{Adjustment_id}}/adjustments/{{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{Adjustment_id}}", "adjustments", "{{account_id}}"]}}, "response": []}, {"name": "Remove adjustments - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{Adjustment_id}}/adjustments/{{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{Adjustment_id}}", "adjustments", "{{account_id}}"]}}, "response": []}, {"name": "Remove adjustments - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{Adjustment_id}}/adjustments/{{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{Adjustment_id}}", "adjustments", "{{account_id}}"]}}, "response": []}, {"name": "Remove adjustments - Without Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ /adjustments/{{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " ", "adjustments", "{{account_id}}"]}}, "response": []}, {"name": "Remove adjustments - Invalid Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/63173/adjustments/{{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "63173", "adjustments", "{{account_id}}"]}}, "response": []}, {"name": "Remove adjustments - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}} /adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}} ", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Remove adjustments - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/adjustments/{{Adjustment_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "adjustments", "{{Adjustment_id}}"]}}, "response": []}, {"name": "Remove adjustments - Blank Adjustment ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/ ", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", " "]}}, "response": []}, {"name": "Remove adjustments - Invalid  Adjustment ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/adjustments/13647", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "adjustments", "13647"]}}, "response": []}, {"name": "Remove adjustments - Blank Both Field", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices//adjustments/", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "", "adjustments", ""]}}, "response": []}]}, {"name": "Get Adjustment Type", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Adjustment Types - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", " pm.test(\"Response body has 'ref' key\", function () {\r", "     const responseBody = pm.response.json();\r", "     pm.expect(responseBody).to.be.hasOwnProperty('ref')\r", " });\r", "\r", "  pm.test(\"Response body has 'name' key\", function () {\r", "     const responseBody = pm.response.json();\r", "     pm.expect(responseBody).to.be.hasOwnProperty('name')\r", " });\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}, {"name": "Adjustment Types - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-ty", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-ty"]}}, "response": []}, {"name": "Adjustment Types - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}, {"name": "Adjustment Types - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}, {"name": "Adjustment Types - Content Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Content-Type is present and is of type JSON\", function () {\r", "    // Check if the \"Content-Type\" header is present\r", "    pm.response.to.have.header(\"Content-Type\");\r", " \r", "    // Get the value of the \"Content-Type\" header\r", "    var contentType = pm.response.headers.get(\"Content-Type\");\r", " \r", "    // Check if the content type is JSON\r", "    if (contentType.includes(\"application/json\")) {\r", "        // Additional assertions for JSON content\r", "        pm.test(\"Response body is valid JSON\", function () {\r", "            pm.response.to.be.json;\r", "        })\r", " \r", "    } else {\r", "        // Handle other content types if needed\r", "        console.log(\"Content-Type is not JSON\");\r", "    }\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}, {"name": "Adjustment Types - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}]}, {"name": "Billing Cycle Report", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Billing Cycle Report - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}/report", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}", "report"]}}, "response": []}, {"name": "Billing Cycle Report - Content Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Content-Type is present and is of type JSON\", function () {\r", "    // Check if the \"Content-Type\" header is present\r", "    pm.response.to.have.header(\"Content-Type\");\r", " \r", "    // Get the value of the \"Content-Type\" header\r", "    var contentType = pm.response.headers.get(\"Content-Type\");\r", " \r", "    // Check if the content type is JSON\r", "    if (contentType.includes(\"application/json\")) {\r", "        // Additional assertions for JSON content\r", "        pm.test(\"Response body is valid JSON\", function () {\r", "            pm.response.to.be.json;\r", "        })\r", " \r", "    } else {\r", "        // Handle other content types if needed\r", "        console.log(\"Content-Type is not JSON\");\r", "    }\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}/report", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}", "report"]}}, "response": []}, {"name": "Billing Cycle Report - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}/report", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}", "report"]}}, "response": []}, {"name": "Billing Cycle Report - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cyclehgf/{{Billing_cycle_date}}/report", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cyclehgf", "{{Billing_cycle_date}}", "report"]}}, "response": []}, {"name": "Billing Cycle Report - Invalid Date Format", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/23-09/report", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "23-09", "report"]}}, "response": []}, {"name": "Billing Cycle Report - Blank Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/ /report", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", " ", "report"]}}, "response": []}, {"name": "Billing cycle report - Without Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/report", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "report"]}}, "response": []}, {"name": "Billing Cycle Report - Response time <500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}/report", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}", "report"]}}, "response": []}, {"name": "Billing Cycle Report - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/cycles/{{Billing_cycle_date}}/report", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "cycles", "{{Billing_cycle_date}}", "report"]}}, "response": []}]}, {"name": "Get Invoice Uses", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Invoice Usage - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-Invalid method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-Invalid  URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoiceswqdsx/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoiceswqdsx", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-Invalid Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_dist9685ributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-Invoice id blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ /usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " ", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage Invoice id Invalid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ *12/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " *12", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Invoice id alphanum", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/5a2b3c/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "5a2b3c", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Invoice id decimal", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/1.25/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "1.25", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Invoice id negative value", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/-{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "-{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-page - valid num", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page alphanum", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1bc&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1bc"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page negative value", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=-1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "-1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-page decimal value", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1.50&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1.50"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -page blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page= &page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": " "}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- Page remove", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page size valid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page size blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size= &ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": " "}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-page size remove", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage page size alphanum", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=2fdc&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "2fdc"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page size symbol", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=***&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "***"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-page size decimal", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=2.50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "2.50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Ordering valid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Ordering blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering= ", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": " "}]}}, "response": []}, {"name": "Invoice Usage -Ordering valid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=rate_plan_name", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "rate_plan_name"}]}}, "response": []}, {"name": "Invoice Usage -Ordering Invalid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=abc123", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "abc123"}]}}, "response": []}, {"name": "Invoice Usage -Ordering Remove", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Invoice Usage -  Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}]}, {"name": "Export Invoice Usage to CSV", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Export invoice Usage to csv - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/213/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "213", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoces/213/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoces", "213", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/213/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "213", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Blank Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{}/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Invalid Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/508df6/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "508df6", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Without Page No", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/213/usage/export?page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "213", "usage", "export"], "query": [{"key": "page", "value": "1.242", "disabled": true}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Without <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 20\", function () {\r", "    pm.response.to.have.status(200);\r", "});0\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/213/usage/export?page=1&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "213", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Valid Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/213/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "213", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Without Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/213/usage/export?page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "213", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/213/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "213", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/213/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "213", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}]}, {"name": "Publish Invoice", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Publish Invoice - 200 & Response time <500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "published"]}}, "response": []}, {"name": "Publish Invoice - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "published"]}}, "response": []}, {"name": "Publish Invoice - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "published"]}}, "response": []}, {"name": "publish Invoice - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoicesewssa/{{invoice_id}}/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoicesewssa", "{{invoice_id}}", "published"]}}, "response": []}, {"name": "Publish Invoice - Without Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ /published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " ", "published"]}}, "response": []}, {"name": "Publish Invoice - Invalid Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/sds54/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "sds54", "published"]}}, "response": []}, {"name": "publish Invoice - Blank Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ /published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " ", "published"]}}, "response": []}]}, {"name": "Unpublished Invoice", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Unpublished invoice - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "published"]}}, "response": []}, {"name": "Unpublished invoice - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "published"]}}, "response": []}, {"name": "Unpublished invoice - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billingyhtgb/invoices/{{invoice_id}}/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billingyhtgb", "invoices", "{{invoice_id}}", "published"]}}, "response": []}, {"name": "Unpublished invoice - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "published"]}}, "response": []}, {"name": "Unpublished invoice - Blank Invoice  ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ /published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " ", "published"]}}, "response": []}, {"name": "Unpublished invoice - Invalid Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/sd45fd/published", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "sd45fd", "published"]}}, "response": []}, {"name": "Delete Account - <PERSON><PERSON> Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts", "{{AccountId}}"]}}, "response": []}]}]}
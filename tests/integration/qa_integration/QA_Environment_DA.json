{"id": "7d7d6f26-4b35-4173-ba0f-44ec659db62e", "name": "QA_Environment_DA", "values": [{"key": "TokenUrl", "value": "https://sso.test.spogconnected.com/auth/realms/test-connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "BaseURL", "value": "https://test.spogconnected.com", "type": "default", "enabled": true}, {"key": "IMSI", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_1", "value": "***************", "type": "default", "enabled": true}, {"key": "month", "value": "2025-04", "type": "default", "enabled": true}, {"key": "account_id", "value": "1", "type": "default", "enabled": true}, {"key": "invoice_id", "value": 395, "type": "default", "enabled": true}, {"key": "rateplanid", "value": 199, "type": "any", "enabled": true}, {"key": "AccountId", "value": 613, "type": "any", "enabled": true}, {"key": "Name", "value": "4gjfyrmz1t", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "secret", "enabled": true}, {"key": "Client_secret", "value": "d3df5208-c809-4063-bfba-c2254ebefd2f", "type": "secret", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "secret", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "secret", "enabled": true}, {"key": "Password", "value": "P@ssw0rd1", "type": "secret", "enabled": true}, {"key": "fromDate", "value": "2025-04-01", "type": "any", "enabled": true}, {"key": "toDate", "value": "2025-04-29", "type": "any", "enabled": true}, {"key": "Adjustment_id", "value": 60, "type": "default", "enabled": true}, {"key": "Billing_cycle_date", "value": "2025-04", "type": "default", "enabled": true}, {"key": "Organisation_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date_Future", "value": "2024-09", "type": "default", "enabled": true}, {"key": "Adjustment date", "value": "2025-04-01", "type": "any", "enabled": true}, {"key": "random_number", "value": "874860246393067", "type": "any", "enabled": true}, {"key": "Random_number", "value": "", "type": "any", "enabled": true}, {"key": "contractEndDate", "value": "2025-04-30", "type": "any", "enabled": true}, {"key": "payment_term", "value": "727", "type": "any", "enabled": true}, {"key": "sim_charge", "value": "06755594433547696259", "type": "any", "enabled": true}, {"key": "group_path", "value": "Nextgen Clearing", "type": "default", "enabled": true}, {"key": "resource_name", "value": "5n43iq1acv", "type": "any", "enabled": true}, {"key": "resource_id", "value": "60f61fb1-638f-45ed-ba65-6ed888a62814", "type": "any", "enabled": true}, {"key": "role_id", "value": "6c2d6150-4eda-4f93-8708-6e726257ab39", "type": "any", "enabled": true}, {"key": "discription", "value": "nlukpffesdvj3i3quwh1", "type": "any", "enabled": true}, {"key": "permission_id", "value": "52df2400-b994-4875-bcbe-7bd814959704", "type": "default", "enabled": true}, {"key": "search_value", "value": "DistributorAdmin", "type": "default", "enabled": true}, {"key": "role_name", "value": "i7zrcrscg7", "type": "default", "enabled": true}, {"key": "resource_scopes_name", "value": "9j80pld", "type": "default", "enabled": true}, {"key": "resource_scopes_display_name", "value": "izpvf2t", "type": "any", "enabled": true}, {"key": "resource_scopes", "value": "bjlx2bp", "type": "any", "enabled": true}, {"key": "permission_name", "value": "7s0av0m81nm4", "type": "default", "enabled": true}, {"key": "scope_id", "value": "ee2828d1-69a0-48ad-acdf-daadeabc7ec8", "type": "default", "enabled": true}, {"key": "group_id", "value": null, "type": "any", "enabled": true}, {"key": "owner", "value": "1j2ak", "type": "any", "enabled": true}, {"key": "account_id_reallocate", "value": "163", "type": "default", "enabled": true}, {"key": "rateplan_id_reallocate", "value": "29", "type": "default", "enabled": true}, {"key": "ICCID", "value": "8944538532046590109", "type": "default", "enabled": true}, {"key": "imsi_reallocate", "value": "**************", "type": "default", "enabled": true}, {"key": "ActiveSIM_ID", "value": "1", "type": "default", "enabled": true}, {"key": "ActiveSIM_Month", "value": "2025-04", "type": "default", "enabled": true}, {"key": "account_id_get_imsi", "value": "8", "type": "default", "enabled": true}, {"key": "Carrier_Type1", "value": "GBRME", "type": "default", "enabled": true}, {"key": "Carrier_Type2", "value": "GBRVF", "type": "default", "enabled": true}, {"key": "Carrier_Type3", "value": "GBRCN", "type": "default", "enabled": true}, {"key": "resource_scopes1", "value": "nj1he8vzvx", "type": "any", "enabled": true}, {"key": "MSISDN", "value": "***************", "type": "default", "enabled": true}, {"key": "file_key", "value": ["temp_file_key", "temp_file_key"], "type": "any", "enabled": true}, {"key": "Rule_Name", "value": "5l4fkrhi<PERSON>", "type": "any", "enabled": true}, {"key": "Data_Volume", "value": 1880, "type": "any", "enabled": true}, {"key": "rule_uuid", "value": "0e564e0b-ab55-4e74-8a61-aaa479957dcd", "type": "any", "enabled": true}, {"key": "notification_id", "value": "6808d8e8ee71dc4555a8147f", "type": "any", "enabled": true}, {"key": "work_id", "value": "68079767549e7f32208be258", "type": "any", "enabled": true}, {"key": "month_adt", "value": "2025-04", "type": "any", "enabled": true}, {"key": "Random_Email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "dataVolume", "value": "null", "type": "any", "enabled": true}, {"key": "rule_ID", "value": "c3d0f796-18cf-4d51-bef3-1e51a6d24780", "type": "any", "enabled": true}, {"key": "IMSI_notification", "value": "234588570010012", "type": "any", "enabled": true}, {"key": "usage_limit_notification", "value": 5, "type": "any", "enabled": true}, {"key": "month_notification", "value": "2024-12", "type": "any", "enabled": true}, {"key": "ICCID_notification", "value": "8944538532046590125", "type": "default", "enabled": true}, {"key": "MSISDN_notification", "value": "883200000110322", "type": "default", "enabled": true}, {"key": "ModelID", "value": 21, "type": "any", "enabled": true}, {"key": "rateplan_id", "value": 214, "type": "any", "enabled": true}, {"key": "CDR_ID", "value": "6808d8deee71dc4555a8147c", "type": "any", "enabled": true}, {"key": "imsi_notification", "value": "234588570010012", "type": "any", "enabled": true}, {"key": "requestTimeout", "value": "true", "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJtOTZBd05DbUVkWGdZVGV5VFdBTDV0SXBnQkwzOW83SVI5dUVlVkx1eEtjIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SiwtF3ivjdispzHe4dv-amXGMGrJin-o4aHK2bL4FbxxES0RdL2J8RmqVeXChj6oSNknYW1SO9AbqwcvJxgs_rd8GDv4V6vAO5EZwRq8200FglszG1IqLJagcRmJggZFkoeE4tuhI6Ffy_Yoh3Cztm7zdi1ytH7wHtYrBzNEGBaJtncrqmylEIk3ZFnHxuoOZbMKt22JczVmG27yunWAEA2NuRQxqC1m5YaPnsxms-RimzSqTZLkJQKclm2w-quslPbKNunZAP9DIWrzK_dXydTCrgXJCtKfz-kxCbWFaPubOk-J3ZDdqGGZAzRz5Zlw-IClbHgMhUmD_S0ClH4R6Q", "type": "secret", "enabled": true}, {"key": "randomTime", "value": "06:30:54", "type": "any", "enabled": true}, {"key": "rateplanid_1", "value": 180, "type": "any", "enabled": true}, {"key": "Target_RatePlan", "value": 21, "type": "any", "enabled": true}, {"key": "Source_RatePlan", "value": 82, "type": "any", "enabled": true}, {"key": "rateplanid_def", "value": 181, "type": "any", "enabled": true}, {"key": "account_id_rp_change", "value": "3", "type": "default", "enabled": true}, {"key": "imsi_rp_change", "value": "***************", "type": "default", "enabled": true}, {"key": "current_hours", "value": "12", "type": "any", "enabled": true}, {"key": "current_minutes", "value": "11", "type": "any", "enabled": true}, {"key": "current_seconds", "value": "15", "type": "any", "enabled": true}, {"key": "Account_id", "value": 186, "type": "default", "enabled": true}, {"key": "rule_category_id", "value": "1", "type": "default", "enabled": true}, {"key": "rule_category_id_2", "value": "4", "type": "default", "enabled": true}, {"key": "Token", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJtOTZBd05DbUVkWGdZVGV5VFdBTDV0SXBnQkwzOW83SVI5dUVlVkx1eEtjIn0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mjIKBjKp5dZfgOBk1xYmc7Og1Rr1LvHwtdBcO_fYM56F6LDX8VnvnZzfbePpaQfMSQOyOyrLyuu5KC3EumWANCbl5iYSrueA6Kk84pM3n4XTBOsuKsc6TeMFI1zyn-AYWDRxQrJSkop22YTqpMK_39mqEKUEBK7bz0Bl0ftOoi8HcfEUY-7qkrQRxmBy9L_Eqad7ouq_YaQAbt13Mf1oWz-9KdSbL2XQvZay0a_nj_2apZdjcUlo6lfHs-JeRbs8o3WLwM0it9Bi2dq2Vq2j2F_AOvJzV75AbTO9BnAziwvNkbmsJUzj5ILv-2qH0VAVxDhZEEvR9QduJxDfdBmC7A", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-04-24T07:18:04.131Z", "_postman_exported_using": "Postman/11.42.3"}
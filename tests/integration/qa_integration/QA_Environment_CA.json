{"id": "7a192769-8786-4ad4-8ca6-7230730f1ffb", "name": "QA_Environment_CA", "values": [{"key": "TokenUrl", "value": "https://sso.test.spogconnected.com/auth/realms/test-connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "BaseURL", "value": "https://test.spogconnected.com", "type": "default", "enabled": true}, {"key": "IMSI", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_1", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_2", "value": "***************", "type": "default", "enabled": true}, {"key": "month", "value": "2025-04", "type": "default", "enabled": true}, {"key": "account_id", "value": "1", "type": "default", "enabled": true}, {"key": "invoice_id", "value": 414, "type": "default", "enabled": true}, {"key": "rateplanid", "value": null, "type": "any", "enabled": true}, {"key": "Name", "value": "alh3ql0l10", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "default", "enabled": true}, {"key": "Client_secret", "value": "d3df5208-c809-4063-bfba-c2254ebefd2f", "type": "secret", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "secret", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "secret", "enabled": true}, {"key": "Password", "value": "S3cur!Ty%G5m", "type": "secret", "enabled": true}, {"key": "Adjustment_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date", "value": "2025-03", "type": "default", "enabled": true}, {"key": "Organisation_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date_Future", "value": "2024-07", "type": "default", "enabled": true}, {"key": "Adjustment date", "value": "2024-07-01", "type": "any", "enabled": true}, {"key": "random_number", "value": "055667218405816", "type": "any", "enabled": true}, {"key": "Random_number", "value": "", "type": "any", "enabled": true}, {"key": "contractEndDate", "value": "2025-04-30", "type": "any", "enabled": true}, {"key": "payment_term", "value": "289", "type": "any", "enabled": true}, {"key": "sim_charge", "value": "98781544148857864903", "type": "any", "enabled": true}, {"key": "group_path", "value": "Nextgen Clearing", "type": "default", "enabled": true}, {"key": "resource_name", "value": "3afu40ttan", "type": "any", "enabled": true}, {"key": "resource_id", "value": null, "type": "any", "enabled": true}, {"key": "role_id", "value": "59ad4de0-8379-4278-8020-e7d783697019", "type": "any", "enabled": true}, {"key": "discription", "value": "cx0c78essku1ruxao2us", "type": "any", "enabled": true}, {"key": "permission_id", "value": "15c99cd0-ed1d-4b31-bc30-5d0833a8466b", "type": "default", "enabled": true}, {"key": "search_value", "value": "ClientAdmin", "type": "default", "enabled": true}, {"key": "role_name", "value": "wkgpeeyznl", "type": "default", "enabled": true}, {"key": "resource_scopes_name", "value": "r7f8l5z", "type": "default", "enabled": true}, {"key": "resource_scopes_display_name", "value": "uvp815c", "type": "any", "enabled": true}, {"key": "resource_scopes", "value": "phpdtcz", "type": "any", "enabled": true}, {"key": "permission_name", "value": "yvenmoyqu5rw", "type": "default", "enabled": true}, {"key": "scope_id", "value": "", "type": "default", "enabled": true}, {"key": "group_id", "value": "", "type": "any", "enabled": true}, {"key": "owner", "value": "798ft", "type": "any", "enabled": true}, {"key": "account_id_reallocate", "value": "163", "type": "default", "enabled": true}, {"key": "rateplan_id_reallocate", "value": "1427", "type": "default", "enabled": true}, {"key": "ICCID", "value": "8944538532046590125", "type": "default", "enabled": true}, {"key": "imsi_reallocate", "value": "***************", "type": "default", "enabled": true}, {"key": "ActiveSIM_ID", "value": "1", "type": "default", "enabled": true}, {"key": "ActiveSIM_Month", "value": "2025-02", "type": "default", "enabled": true}, {"key": "account_id_get_imsi", "value": "2", "type": "default", "enabled": true}, {"key": "Carrier_Type1", "value": "GBRME", "type": "default", "enabled": true}, {"key": "Carrier_Type2", "value": "GBRVF", "type": "default", "enabled": true}, {"key": "Carrier_Type3", "value": "GBRCN", "type": "default", "enabled": true}, {"key": "Rule_Name", "value": "nh66lw9hmz", "type": "any", "enabled": true}, {"key": "Data_Volume", "value": 1820, "type": "any", "enabled": true}, {"key": "rule_uuid", "value": "85b0ae07-5bfd-4830-900c-16c74794e764", "type": "any", "enabled": true}, {"key": "Random_Email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "AccountId", "value": "1", "type": "any", "enabled": true}, {"key": "notification_id", "value": "6808cd48ee71dc4555a8142b", "type": "any", "enabled": true}, {"key": "cdr_ID", "value": "*********", "type": "any", "enabled": true}, {"key": "rule_ID", "value": "6afda784-d763-45aa-a4f5-dcd382b7e865", "type": "any", "enabled": true}, {"key": "IMSI_notification", "value": "***************", "type": "any", "enabled": true}, {"key": "month_notification", "value": "2024-09", "type": "any", "enabled": true}, {"key": "usage_limit_notification", "value": 504, "type": "any", "enabled": true}, {"key": "fromDate", "value": "2025-04-01", "type": "any", "enabled": true}, {"key": "toDate", "value": "2025-04-29", "type": "any", "enabled": true}, {"key": "file_key", "value": ["2024-07-10/2024-07-10_4dc7b3bb-7e80-45b4-9cd6-2a748840655b.xml", "2024-07-19/2024-07-19_0e7daa68-c83e-4c19-8e43-43f0e00443a7.xml"], "type": "any", "enabled": true}, {"key": "currentHour", "value": "12", "type": "any", "enabled": true}, {"key": "currentMinute", "value": "08", "type": "any", "enabled": true}, {"key": "currentSecond", "value": "56", "type": "any", "enabled": true}, {"key": "ValueIncrease", "value": 7, "type": "any", "enabled": true}, {"key": "MSISDN", "value": "883200000110322", "type": "any", "enabled": true}, {"key": "dataVolume", "value": 7, "type": "any", "enabled": true}, {"key": "MSISDN_notification", "value": "883200000110322", "type": "default", "enabled": true}, {"key": "ICCID_notification", "value": "8944538532046590125", "type": "default", "enabled": true}, {"key": "CDR_ID", "value": "6808cd3e58d316fcc0daffb6", "type": "any", "enabled": true}, {"key": "resource_scopes1", "value": "12sqck1yxh", "type": "any", "enabled": true}, {"key": "work_id", "value": null, "type": "any", "enabled": true}, {"key": "month_adt", "value": "2025-04", "type": "any", "enabled": true}, {"key": "randomTime", "value": "07:14:46", "type": "any", "enabled": true}, {"key": "ModelID", "value": 90, "type": "any", "enabled": true}, {"key": "Source_RatePlan", "value": 82, "type": "any", "enabled": true}, {"key": "Target_RatePlan", "value": 21, "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJtOTZBd05DbUVkWGdZVGV5VFdBTDV0SXBnQkwzOW83SVI5dUVlVkx1eEtjIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OgWMSJFKkHJO3prhLdOjPm_Lgvy6oCT0NFW0CdaqI287zSX9htO7RGTnjhsEf9mEMNQhIViHzqYFdyAL9fxD2THCnyzHruO63JshUhu2biOAFs3a3dUY9EtTlnmvShG-cIk1cVHmoAfXG75AFgzjqBVOi4-4eHwwMeZpVX5RaZYwAOdgxaPq-3abnDCqi9enhF4rBUW0GwQCJh8l1BBzNLjNGS-i4qca_ueJhgHAoV3LlvLdCYYc-q6_m2ZyDJ8LloJlVRQ4cvnL3t0RUogcMeZjUBXh8sEqAc1ke753-HZtqUhLSuoIgG32vj9xhiZF5hvVS8u0pE_iyPQLWc63fw", "type": "any", "enabled": true}, {"key": "current_hours", "value": "16", "type": "any", "enabled": true}, {"key": "current_minutes", "value": "51", "type": "any", "enabled": true}, {"key": "current_seconds", "value": "39", "type": "any", "enabled": true}, {"key": "IMSI_Real", "value": "***************", "type": "default", "enabled": true}, {"key": "rule_category_id", "value": "1", "type": "default", "enabled": true}, {"key": "rule_category_id_2", "value": "4", "type": "default", "enabled": true}, {"key": "rateplan_id", "value": 21, "type": "any", "enabled": true}, {"key": "account_id_rp_change", "value": "3", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-04-24T07:18:15.092Z", "_postman_exported_using": "Postman/11.42.3"}